<template>
  <div class="app-container">
    <div class="title">配网工程设计任务管理</div>
    <data-dialog v-if="dialogVisible" dataWidth="700" @close="closeDialog">
      <template #header>
        <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
          方案提交
        </h4>
      </template>
      <template #body>
        <el-row class="line">
          <el-col :span="4" class="line-item">
            <span> 停电线路 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="form.backLine"
              placeholder="请输入文本"
            ></el-input>
          </el-col>
          <el-col :span="4" class="line-item">
            <span> 停电范围 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="form.backRange"
              placeholder="请输入文本"
            ></el-input>
          </el-col>
        </el-row>
        <el-row class="line">
          <el-col :span="4" class="line-item">
            <span> 停电区域工程量 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="form.backJob"
              placeholder="请输入文本"
            ></el-input>
          </el-col>
          <el-col :span="4" class="line-item">
            <span>配变数量</span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="form.backNum"
              min="1"
              placeholder="请输入整数"
              type="number"
            ></el-input>
          </el-col>
        </el-row>
        <el-row class="line">
          <el-col :span="4" class="line-item">
            <span>预计停电时长</span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="form.backTime"
              min="1"
              placeholder="请输入整数"
              type="number"
            ></el-input>
          </el-col>
          <el-col :span="4" class="line-item">
            <span> 预计停电时户数 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="form.backNumber"
              min="1"
              placeholder="请输入整数"
              type="number"
            ></el-input>
          </el-col>
        </el-row>
        <el-row class="line">
          <el-col :span="4" class="line-item" style="height: 115px">
            <span>未开展不停电作业原因分析</span>
          </el-col>
          <el-col :span="20" class="line-input" style="height: auto">
            <el-input
              v-model="form.backReason"
              :rows="5"
              placeholder="请输入文本"
              type="textarea"
            ></el-input>
          </el-col>
        </el-row>
        <div
          class="line"
          style="background: #e4f2f2; height: 50px; justify-content: center"
        >
          <div class="line-bnt" @click="getClick1">确认提交</div>
        </div>
      </template>
    </data-dialog>

    <!-- 规模统计 -->
    <el-dialog
      v-model="dialogVisible1"
      :show-close="true"
      class="data-dialogWl"
      title="规模统计"
      width="600"
    >
      <template #header>
        <div class="scale-header">
          <h4>规模统计</h4>
        </div>
      </template>
      <div style="height: 337px; margin-top: -16px">
        <div class="line">
          <div class="line-item">
            <span> 架空线路(km) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.overheadLine"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> 电力电缆(km) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.powerCable"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 低压线路(km) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.lowPressureLine"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> 低压电缆(km) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.lowPressureCable"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 配电变压器(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.transformer"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> 配电变压器(kVA) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.transformerVoltage"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 环网柜(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.ringMainUnit"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> DTU(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.dtu"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 普通开关(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.ordinarySwitch"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> 智能开关(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.intelligentSwitch"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 柱上变(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.columnVariation"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> 箱变(台) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.boxTransformer"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 水泥杆(基) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.cementPole"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item">
            <span> 钢管杆(基) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.steelPipePole"
              placeholder=""
            ></el-input>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 铁塔(基) </span>
          </div>
          <div class="line-input">
            <el-input
              class="in-item"
              type="number"
              min="0"
              v-model="dataScaleInfo.ironTower"
              placeholder=""
            ></el-input>
          </div>
          <div class="line-item"></div>
          <div class="line-input"></div>
        </div>
        <div
          class="line"
          style="background: #e4f2f2; height: 50px; justify-content: center"
        >
          <div class="line-bnt" @click="getClick">确认提交</div>
        </div>
      </div>
    </el-dialog>
    <el-divider />
    <div style="display: flex; align-items: center">
      <div>
        <el-select
          v-model="queryParams.type"
          class="custom-select"
          placeholder="请选择"
          style="width: 240px; margin: 0 20px"
          @change="queryTypeChange"
        >
          <el-option label="项目名称" value="项目名称" />
          <el-option
            v-if="!queryParams.checked"
            label="任务状态"
            value="任务状态"
          />
        </el-select>
      </div>
      <div>
        <el-select
          v-if="queryParams.type === '任务状态'"
          v-model="queryParams.value"
          :disabled="queryParams.checked && queryParams.type === '任务状态'"
          class="custom-select"
          placeholder="请选择"
          style="width: 300px"
        >
          <el-option label="未开始" value="0" />
          <el-option label="进行中" value="1" />
          <!-- <el-option label="已完成" value="2" /> -->
          <el-option label="移交中" value="3" />
          <el-option label="已退回" value="4" />
        </el-select>
        <el-input
          v-if="queryParams.type === '项目名称'"
          v-model="queryParams.value"
          class="custom-input"
          clearable
          placeholder="查询条件, 当输入信息为空时,重置任务列表"
          style="width: 300px"
        >
        </el-input>
        <el-button
          :disabled="queryParams.checked && queryParams.type === '任务状态'"
          color="#50adaa"
          icon="Search"
          style="color: #fff"
          @click="getList"
        />
      </div>
      <div>
        <el-checkbox
          v-model="queryParams.checked"
          class="custom-checkbox"
          label="已完成任务"
          size="large"
          style="margin-left: 5vw"
          @change="getList"
        />
      </div>
    </div>
    <!--   <el-form style="margin-top: 10px" :model="queryParams" ref="queryRef" :inline="true" label-width="68px">-->
    <!--      <el-form-item label="项目名称">-->

    <!--      </el-form-item>-->
    <!--      <el-form-item label="任务名称">-->

    <!--      </el-form-item>-->
    <!--      <el-form-item>-->
    <!--         -->
    <!--      </el-form-item>-->
    <!--   </el-form>-->
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      style="margin-top: 15px"
      type="card"
      @tab-change="handleClick"
    >
      <el-tab-pane label="施工图设计任务" name="1">
        <el-table
          v-loading="loading"
          :data="taskList"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          class="demo-table"
          row-key="menuId"
          stripe
        >
          <el-table-column
            :show-overflow-tooltip="true"
            label="子项目名称"
            prop="projectName"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="营销流程号"
            prop="projectCode"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="所属市公司"
            prop="cityOrganisationName"
            width="120"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="所属县公司"
            prop="countyOrganisationName"
            width="120"
          ></el-table-column>
          <el-table-column
            label="电压等级"
            prop="voltageLevel"
            width="80"
          ></el-table-column>
          <el-table-column label="任务下达时间" prop="createTime" width="110">
            <template #default="scope">
              <el-tooltip
                :content="scope.row.createTime"
                class="item"
                effect="dark"
                placement="top"
              >
                <span>{{ formatDate(scope.row.createTime) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="任务状态" prop="state" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else-if="scope.row.state === '0'">未开始</span>
              <span v-else-if="scope.row.state === '3'">移交中</span>
              <span v-else-if="scope.row.state === '4'">已退回</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop=""
            label="数据来源"
            width="80"
          ></el-table-column> -->
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column
            :width="queryParams.checked ? '150' : '400'"
            align="center"
            class-name="small-padding fixed-width"
            label="操作"
          >
            <template #default="scope">
              <!--            <el-button color="#0e8b8f" style="color: #fff;" :disabled="true" round>获取</el-button>-->
              <div>
                <el-button
                  v-if="queryParams.checked"
                  color="red"
                  round
                  style="color: #fff"
                  @click="htBtn(scope.row, '1')"
                  >回退
                </el-button>
                <el-button
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  round
                  type="success"
                  @click="csClick(scope.row)"
                  >参数设置</el-button
                >
                <!-- <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  round
                  type="success"
                  @click="jccsBtn(scope.row)"
                  >基础参数设置</el-button
                >
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  round
                  type="warning"
                  @click="gccsBtn(scope.row)"
                  >工程参数设置</el-button
                > -->
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  color="#1abc9c"
                  round
                  style="color: #fff"
                  @click="startOnlineDesgin(scope.row, false)"
                  >设计
                </el-button>
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': scope.row.state !== '1' }"
                  :disabled="scope.row.state !== '1'"
                  round
                  type="primary"
                  @click="btnMoveEngineeringDesiger(scope.row)"
                  >移交</el-button
                >
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': scope.row.state !== '1' }"
                  :disabled="scope.row.state !== '1'"
                  round
                  type="primary"
                  @click="schemeSubmit(scope.row)"
                  >方案提交</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          v-model:limit="queryParams.size"
          v-model:page="queryParams.current"
          :total="total"
          class="pagination"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="竣工图设计任务" name="2">
        <el-table
          v-loading="loading"
          :data="taskList"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          class="demo-table"
          row-key="menuId"
          stripe
        >
          <el-table-column
            :show-overflow-tooltip="true"
            label="子项目名称"
            prop="projectName"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="营销流程号"
            prop="projectCode"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="所属市公司"
            prop="cityOrganisationName"
            width="120"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            label="所属县公司"
            prop="countyOrganisationName"
            width="120"
          ></el-table-column>
          <el-table-column
            label="电压等级"
            prop="voltageLevel"
            width="80"
          ></el-table-column>
          <el-table-column label="任务下达时间" prop="createTime" width="110">
            <template #default="scope">
              <el-tooltip
                :content="scope.row.createTime"
                class="item"
                effect="dark"
                placement="top"
              >
                <span>{{ formatDate(scope.row.createTime) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="任务状态" prop="state" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span v-else-if="scope.row.state === '0'">未开始</span>
              <span v-else-if="scope.row.state === '3'">移交中</span>
              <span v-else-if="scope.row.state === '4'">已退回</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop=""
            label="数据来源"
            width="80"
          ></el-table-column> -->
          <!--      <el-table-column prop="sjly" label="获取状态" width="80"></el-table-column>-->
          <el-table-column
            :width="queryParams.checked ? '150' : '400'"
            align="center"
            class-name="small-padding fixed-width"
            label="操作"
          >
            <template #default="scope">
              <!--            <el-button color="#0e8b8f" style="color: #fff;" :disabled="true" round>获取</el-button>-->

              <div>
                <el-button
                  v-if="queryParams.checked"
                  color="red"
                  round
                  style="color: #fff"
                  @click="htBtn(scope.row, '1')"
                  >回退
                </el-button>
                <!-- <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  round
                  type="success"
                  @click="jccsBtn(scope.row)"
                  >基础参数设置</el-button
                >
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  round
                  type="warning"
                  @click="gccsBtn(scope.row)"
                  >工程参数设置</el-button
                > -->
                <el-button
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  round
                  type="success"
                  @click="csClick(scope.row)"
                  >参数设置</el-button
                >
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': queryParams.checked }"
                  :disabled="queryParams.checked"
                  color="#1abc9c"
                  round
                  style="color: #fff"
                  @click="startOnlineDesgin(scope.row, false)"
                  >设计
                </el-button>
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': scope.row.state !== '1' }"
                  :disabled="scope.row.state !== '1'"
                  round
                  type="primary"
                  @click="btnMoveEngineeringDesiger(scope.row)"
                  >移交</el-button
                >
                <el-button
                  v-if="!queryParams.checked"
                  :class="{ 'is-disabled': scope.row.state !== '1' }"
                  :disabled="scope.row.state !== '1'"
                  round
                  type="primary"
                  @click="schemeSubmit(scope.row)"
                  >方案提交</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          v-model:limit="queryParams.size"
          v-model:page="queryParams.current"
          :total="total"
          class="pagination"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 参数设置 -->
    <el-dialog
      v-model="dialogFlag"
      append-to-body
      :title="titles"
      width="1000px"
      @close="close"
    >
      <el-tabs
        v-model="activeNames"
        class="demo-tabs"
        type="card"
        @tab-click="handleTab"
      >
        <el-tab-pane name="jc" label="基础参数设置">
          <basic-parameter-setting :taskId="taskId"></basic-parameter-setting>
        </el-tab-pane>
        <el-tab-pane name="gc" label="工程参数设置">
          <DialogGccs :taskId="taskId"></DialogGccs>
        </el-tab-pane>
        <el-tab-pane name="js " label="计算参数设置">
          <parameter-calculation style="padding: 0"></parameter-calculation>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <reuse-project
      v-model="isDialogVisible2"
      :data="reuseList"
      @cancelDialog="isDialogVisible2 = false"
      @close="isDialogVisible2 = false"
      @subStart="handleSubStart"
    >
    </reuse-project>
  </div>
</template>
<script name="Menu" setup>
import DataDialog from "@/components/DataDialog/index.vue";
import DialogGccs from "@/views/desginManage/task/components/DialogGccs.vue";
import basicParameterSetting from "@/views/desginManage/task/components/basicParameterSetting.vue";
import parameterCalculation from "@/views/config-management/parameter-calculation";
import reuseProject from "@/views/desginManage/task/components/reuseProject.vue";
import { ElLoading } from "element-plus";
import {
  getDicts,
  getEngineering,
  getEngineeringParameter,
  getInfoById,
  rollbackEngineering,
  simulateSubmit,
  simulateSubmit2,
  changeEngineeringStage,
  moveEngineeringDesiger,
  rollback,
  yijiao,
  qianshou,
} from "@/api/desginManage/GccsDialog.js";
import { getScaleStatistics } from "@/api/desginManage/ResultsManagement.js";
import { chengGuoShangBao, chengguofuyong } from "@/api/insertSag/index.js";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { handleThemeStyle } from "@/utils/theme.js";
import useSettingsStore from "@/store/modules/settings.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import {
  getBaseSettingList,
  getEngineeringSettingByTaskId,
} from "@/api/taskBaseData/index.js";
import { copyEngineeringReuse } from "@/api/desginManage/GccsDialog.js";
import { getCalculationSettingList } from "@/api/config-management/parameter-calculation.js";
import useAppStore from "@/store/modules/app";
let { proxy } = getCurrentInstance();
const appStore = useAppStore();
const dialogFlag = ref(false);
const dialogVisible = ref(false);
const dialogVisible1 = ref(false);
appStore.clickData(true);
const closeDialog = () => {
  dialogVisible.value = false;
};
const activeNames = ref("jc");
const projectInfoStore = useProjectInfoStore();
const router = useRouter();
const voltageLevelList = {
  "08": "0.4kV",
  22: "10kV",
  24: "20kV",
  25: "35kV",
};
const isDialogVisible = ref(false);
const isDialogVisible1 = ref(false);
const isDialogVisible2 = ref(false);
const getEngineeringList = ref({});
const loading = ref(false);
const isExpandAll = ref(false);
const gccsList = ref({});
const qxqList = ref([]);
const taskList = ref([]);
const activeName = ref("2");
const taskId = ref("");
const total = ref(1);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    type: "项目名称",
    value: "",
    checked: false,
    projectName: "",
    status: "",
    stage: "1",
  },
});
const form = ref({
  backLine: "",
  backRange: "",
  backJob: "",
  backNum: "",
  backTime: "",
  backNumber: "",
  backReason: "",
});
const titles = ref("基础参数设置");
const dataScaleInfo = ref({});
const getClick1 = () => {
  dialogVisible.value = false;
  dialogVisible1.value = true;
  getScaleStatistics({ taskId: taskId.value }).then((res) => {
    if(res.code=='200'){
    dataScaleInfo.value = res.data;

    }else{
      dataScaleInfo.value.boxTransformer=0
      dataScaleInfo.value.cementPole=0
      dataScaleInfo.value.dtu=0
      dataScaleInfo.value.columnVariation=0
      dataScaleInfo.value.intelligentSwitch=0
      dataScaleInfo.value.ironTower=0
      dataScaleInfo.value.lowPressureCable=0
      dataScaleInfo.value.lowPressureLine=0
      dataScaleInfo.value.ordinarySwitch=0
      dataScaleInfo.value.overheadLine=0
      dataScaleInfo.value.powerCable=0
      dataScaleInfo.value.ringMainUnit=0
      dataScaleInfo.value.steelPipePole=0
      dataScaleInfo.value.transformer=0
      dataScaleInfo.value.transformerVoltage=0
    }
  });
};
const handleTab = (val) => {
  if (val.props.name == "jc") {
    titles.value = "基础参数设置";
  } else if (val.props.name == "gc") {
    titles.value = "工程参数设置";
  } else {
    titles.value = "计算参数设置";
  }
};
const csClick = (row) => {
  taskId.value = row.objId;
  activeNames.value = "jc";
  dialogFlag.value = true;
};
const getClick = () => {
  ElMessageBox.confirm("是否完成设计，确定提交方案?", "消息", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: "加载中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    const newObj = convertToNumbers(dataScaleInfo.value);
    let options = {
      tdsyhxx: {
        PowerFailureLine: form.value.backLine,
        PowerFailureAre: form.value.backRange,
        PowerFailureQuantities: form.value.backJob,
        TransformerNum: form.value.backNum,
        PowerFailureDuration: form.value.backTime,
        PowerFailureUserNum: form.value.backNumber,
        PowerFailurereason: form.value.backReason,
      },
      scaleStatistics: newObj,
      ywwym: taskId.value,
      stage: stage.value,
    };
    chengGuoShangBao(options).then((res) => {
      console.log("🚀 ~ chengGuoShangBao ~ res:", res);
      if (res.code === 200) {
        proxy.$modal.msgSuccess("操作成功");
        dialogVisible1.value = false;
        loading.close();
        getList();
      } else {
        proxy.$modal.msgWarning(res.msg);
        loading.close();
        dialogVisible1.value = false;
      }
    });
  });
};
function convertToNumbers(obj) {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    acc[key] = typeof value === "string" && !isNaN(value) ? +value : value;
    return acc;
  }, {});
}
const { queryParams } = toRefs(data);
const gccsBtn = (row) => {
  taskId.value = row.objId;
  isDialogVisible.value = true;
  getEngineeringParameter({ engineeringId: row.objId }).then((res) => {
    getEngineeringList.value = res.data;
  });
};

const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
  }
};
const handleClick = (val) => {
  getList();
};
const getList = async () => {
  queryParams.value.stage = activeName.value;
  if (queryParams.value.checked) {
    queryParams.value.status = "2";
  } else {
    queryParams.value.status = "";
  }
  if (queryParams.value.type === "项目名称") {
    queryParams.value.projectName = queryParams.value.value;
  } else if (queryParams.value.type === "任务状态") {
    queryParams.value.status = queryParams.value.value;
  } else {
    queryParams.value.projectName = "";
    queryParams.value.status = "";
  }

  const res = await getEngineering(queryParams.value);
  taskList.value = res.data.records;
  total.value = res.data.total;
};
const queryTypeChange = () => {
  queryParams.value.projectName = "";
  queryParams.value.status = "";
  queryParams.value.value = "";
};

// 回退
const htBtn = (row, status) => {
  ElMessageBox.confirm("确定要进行此操作嘛?", "消息", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      rollback(row.objId).then((res) => {
        ElMessageBox.alert(res.msg, "消息", {
          confirmButtonText: "OK",
          callback: (action) => {
            getList();
          },
        });
      });
    })
    .catch(() => {});
};

const btnMoveEngineeringDesiger = (row) => {
  ElMessageBox.confirm("确定要进行此操作嘛?", "消息", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    yijiao(row.objId).then((res) => {
      if (res.code === 200) {
        ElMessage.success("移交成功");
        getList();
      } else {
        ElMessage.error(res.msg);
      }
      console.log("moveEngineeringDesiger", res);
    });
  });
};

let handleSubStartList = ref([]);
const handleSubStart = (item) => {
  handleSubStartList.value = item;
  copyEngineeringReuse({ id: objList.value.objId, id2: item.objId }).then(
    (res) => {
      const loading = ElLoading.service({
        lock: true,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (res.code == "200") {
        startOnlineDesgin(item, true, true);
      }
      loading.close();
    }
  );
};
let reuseList = ref([]);
let objList = ref({});
let copyObj = ref({});
/*启动在线设计*/
async function startOnlineDesgin(row, flag, startFlag) {
  if (!startFlag) {
    copyObj.value = row;
  }
  if (row.isFuyong == null || row.isFuyong === "0") {
    flag = false;
  } else {
    flag = true;
  }
  if (row.stage != "2" && !flag) {
    objList.value = {
      isFuyong: row.isFuyong,
      objId: row.objId,
    };
    chengguofuyong(row.objId).then((res) => {
      if (res.data.length == 0) {
        ElMessage.error("复用上个工程成果失败！");
        return;
      }
      reuseList.value = res.data;
      isDialogVisible2.value = true;
    });
  } else {
    if (!startFlag) {
      objList.value = {};
    }
    console.log("启动在线设计");
    isDialogVisible2.value = false;
    projectInfoStore.addProjectInfo(row);
    // if(row.stage==="2"){
    //   row.objId=row.projectWholeprocessId
    // }
    const params = {
      id: objList.value.objId ? objList.value.objId : row.objId,
    };
    //获取地理条件设置 气象区
    const geographicConditions = await getEngineeringSettingByTaskId({
      taskId: objList.value.objId ? objList.value.objId : row.objId,
    });
    if (geographicConditions.code === 200) {
      projectInfoStore.addGeographicConditionsSetting({
        objId: objList.value.objId ? objList.value.objId : row.objId,
        data: JSON.parse(geographicConditions.msg),
      });
    } else {
      ElMessage.error("获取地理条件失败！");
    }
    const baseSettings = await getBaseSettingList({
      taskId: objList.value.objId ? objList.value.objId : row.objId,
    });
    if (baseSettings.code === 200) {
      projectInfoStore.addBaseSetting(baseSettings.data);
    } else {
      ElMessage.error("获取基础参数失败！");
    }
    const calculationSetting = await getCalculationSettingList({taskId: objList.value.objId ? objList.value.objId : row.objId});
    if (calculationSetting.code === 200) {
      projectInfoStore.addCalculationSetting(
        calculationSetting.data.calculationSetting
      );
    } else {
      ElMessage.error("获取计算参数失败！");
    }
    qianshou(objList.value.objId ? objList.value.objId : row.objId)
      .then((res) => {
        if (res.code === 200) {
          // ElMessage.success('操作成功')
          getList();
          let lineInfos = "";
          let wgBorders = "";
          if (row.lineInfos && row.lineInfos.length > 0) {
            //存在馈线
            lineInfos = JSON.stringify(
              row.lineInfos.map((item) => {
                return {
                  transformerRoomCode: item.transformerRoomCode,
                  lineCode: item.lineCode,
                  isFeeder: item.isFeeder,
                };
              })
            );
            lineInfos = encodeURIComponent(lineInfos);
          }
          if (row.wgBorders && row.wgBorders.length > 0) {
            //存在网格
            wgBorders = JSON.stringify(
              row.wgBorders.map((item) => {
                return {
                  wgborderCode: item.wgborderCode,
                };
              })
            );
            wgBorders = encodeURIComponent(wgBorders);
          }
          const c = router.resolve({
            name: "OnlineDesign",
            query: {
              id: copyObj.value.objId ? copyObj.value.objId : row.objId,
              stage: copyObj.value.stage ? copyObj.value.stage : row.stage,
              proProcessId: copyObj.value.projectWholeprocessId
                ? copyObj.value.projectWholeprocessId
                : row.projectWholeprocessId,
              compilingOrganisationName: copyObj.value.compilingOrganisationName
                ? copyObj.value.compilingOrganisationName
                : row.compilingOrganisationName,
              proCode: copyObj.value.projectCode
                ? copyObj.value.projectCode
                : row.projectCode,
              engCode: copyObj.value.engineeringCode
                ? copyObj.value.engineeringCode
                : row.engineeringCode,
              workCode: copyObj.value.workOrdercode
                ? copyObj.value.workOrdercode
                : row.workOrdercode,
              processCode: copyObj.value.processCode
                ? copyObj.value.processCode
                : row.processCode,
              countyOrganisationName: copyObj.value.countyOrganisationName
                ? copyObj.value.countyOrganisationName
                : row.countyOrganisationName,
              projectName: copyObj.value.projectName
                ? copyObj.value.projectName
                : row.projectName,
              lineInfos,
              wgBorders,
              oldVersionId: startFlag ? row.versionId : "",
              startFlag: startFlag,
            },
          });
          window.open(c.href, "_blank");
          oniframeMessage({
            type: "greeting",
            content: "SDK_loadMap",
            options: { lineIds: lineIds },
          });
        } else {
          ElMessage.error(res.msg);
        }
      })
      .catch(() => {});
  }
}
let stage = ref("");
// 方案提交
const schemeSubmit = (row) => {
  form.value = {
    backLine: "",
    backRange: "",
    backJob: "",
    backNum: "",
    backTime: "",
    backNumber: "",
    backReason: "",
  };
  stage.value = row.stage;
  taskId.value = row.objId;
  dialogVisible.value = true;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
// @use "@/views/pages/online/components/index" as *;
@use "../../pages/online/components/index.scss" as *;
//按钮

.line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;

  :last-child {
    margin-bottom: 0;
  }

  .line-item {
    width: 184px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }

  .line-input {
    // flex: 1;
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    .in-item {
      width: 157px;
      height: 27px;
    }
  }

  .line-no-display {
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }

  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}

.scale-header {
  padding: 10px 16px;
  margin: 0px;
  h4 {
    margin: 0;
    font-size: 16px;
    color: var(--el-text-color-primary);
    font-weight: 600;
    line-height: 24px;
  }
}
.title {
  color: #0e8b8f;
  font-size: 20px;
  font-weight: 700;
}

.demo-tabs ::v-deep .el-tabs__header {
  background-color: #fff; /* Tab栏背景颜色 */
}

.demo-tabs ::v-deep .el-tabs__item.is-active {
  color: #fff; /* 选中Tab的字体颜色 */
  background-color: #50adaa; /* 选中Tab的背景颜色 */
}

.demo-tabs ::v-deep .el-tabs__item {
  color: #333; /* 未选中Tab的字体颜色 */
}

/* 设置表头的背景颜色 */
::v-deep .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background-color: #d7d7d7 !important; /* 自定义表头背景颜色 */
}

/* 设置表头字体的颜色 */
.demo-table ::v-deep .el-table__header th {
  color: #000; /* 自定义字体颜色 */
  font-weight: 700;
}

::v-deep .el-input-group__append {
  background-color: #50adaa;

  .el-icon {
    color: #fff;
  }
}

::v-deep .el-divider--horizontal {
  margin: 15px 0;
}

/* 修改选中复选框的勾选颜色 */
::v-deep .custom-checkbox.is-checked .el-checkbox__inner {
  background-color: #50adaa; /* 设置选中复选框的背景颜色 */
  border-color: #50adaa; /* 设置选中复选框的边框颜色 */
}

/* 设置勾选后的勾选标记颜色 */
::v-deep .custom-checkbox.is-checked .el-checkbox__inner::after {
  border-color: white; /* 设置勾选标记的颜色 */
}

/* 修改选中复选框时的文字颜色 */
::v-deep .custom-checkbox.is-checked .el-checkbox__label {
  color: #50adaa; /* 设置选中状态文字颜色 */
}

::v-deep .el-pager li.is-active {
  background-color: #50adaa !important;
}

// 下拉框选项边框
::v-deep .is-focused {
  box-shadow: 0 0 0 1px #50adaa inset;
}

// 输入边框
.custom-input ::v-deep .is-focus {
  box-shadow: 0 0 0 1px #50adaa inset;
}

.pagination ::v-deep .is-focus {
  box-shadow: 0 0 0 1px #50adaa inset;
}

.el-select-dropdown__item.is-selected {
  color: #50adaa;
}

.is-disabled {
  background-color: #dcdcdc !important;
  border-color: #dcdcdc !important;
  color: #ffffff !important;
  cursor: not-allowed !important;
}
</style>
