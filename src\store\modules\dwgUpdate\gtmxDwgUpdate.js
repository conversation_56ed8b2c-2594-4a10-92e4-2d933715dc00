
import {
    getTowerDetails,
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
import { getLegendState } from "@/views/pages/online/commonData.js";
const gtmxDwgUpdate = defineStore(
    'gtmxDwgUpdate',
    {
        state: () => ({
            cjwzTableData: undefined,
        }),
        actions: {
            async gtmxDwgUpdateFun(th) {
                return new Promise((resolve, reject) => {
                    getTowerDetails({ taskId: publicStore().taskId }).then(res => {
                        try {
                            if (res.code == 200) {
                                res.data.forEach(element => {
                                    element.towerStatus = getLegendState(element.towerStatus)
                                    element.wireStatus = getLegendState(element.wireStatus)
                                    element.towerPoleStatus = getLegendState(element.towerPoleStatus)
                                });
                                resolve({ data: res.data, fileBlobArr: [], th: th })
                            }
                        } catch (error) {
                            reject(error)
                        }

                    })
                })

            },

        },
        persist: true
    })
export default gtmxDwgUpdate
