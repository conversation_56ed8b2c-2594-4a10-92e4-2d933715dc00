{"name": "mxcad-plugin", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite build --watch --mode=debug | vite preview --port 3366 --outDir ../dist --config ./vite.config.ts", "build": "vite build"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "mxcad": "^1.0.246", "mxdraw": "latest", "sass": "^1.66.1", "vite": "^4.4.4", "vue": "^3.3.4"}, "dependencies": {"child_process": "^1.0.2", "fs": "^0.0.1-security", "mapbox-gl": "2.8.2", "mxcad-plugin": "file:", "qrcode-generator": "^1.4.4", "svgson": "^5.3.1", "three": "0.113.2"}}