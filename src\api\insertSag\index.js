import request from '@/utils/request'

//成果上报
export function chengGuoShangBao(data) {
    return request({
        url: '/chengGuoShangBao/submit',
        method: 'post',
        data: data,
    })
}
//#region 成果输出
//插入应力弧垂表接口-导线型号
export function getYlhcbByTaskId(params){
    return request({
        url: '/equipmentInfo/getYlhcbByTaskId/' + params,
        method: 'get',
    })
}
//插入应力弧垂表接口-安全系数
export function getDxaqxsByTaskIdModuleId(taskId, moduleId){
    return request({
        url: '/equipmentInfo/getDxaqxsByTaskIdModuleId/' + taskId + '/' + moduleId,
        method: 'get',
    })
}
//插入应力弧垂表接口-保存
export function ylhcbInsertTUzhi(data){
    return request({
        url: '/equipmentInfo/ylhcbInsertTUzhi',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
//钢管杆明细表 -列表
export function getGggMx(taskId){
    return request({
        url: '/equipmentInfo/getGggMx/' + taskId ,
        method: 'get',
    })
}
// 钢管杆明细表-生成报表
export function generatorGggmxReport(data) {
    return request({
        url: '/system/specification/generatorGggmxReport',
        method: 'post',
        data: data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}

//应力弧垂表 多个
export function getYlhcbFileNameList(data){
    return request({
        url: '/equipmentInfo/getYlhcbFileNameList',
        method: 'post',
        data
    })
}
//电缆通道土建表
export function getCableChannelDetails(data){
    return request({
        url: '/equipmentInfo/getCableChannelDetails',
        method: 'get',
        params:data
    })
}
//电缆通道土建表 --保存
export function generatorDltdtjReport(data) {
    return request({
        url: '/system/specification/generatorDltdtjReport',
        method: 'post',
        data: data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
//拆旧物资清册 物料类别
export function getAllMaterialTypes() {
    return request({
        url: '/base/legendType/getAllMaterialTypes',
        method: 'get',
    })
}
//拆旧物资清册 生成报表
export function saveRemoveUsingMaterials(params) {
    return request({
        url: '/api/material-calculation/saveRemoveUsingMaterials',
        method: 'post',
        data:params
    })
}
export function querycalculate(params) {
    return request({
        url: '/api/material-calculation/calculate',
        method: 'get',
        params: params,
    })
}
//自定义库导入模板
export function materialCustomImportExcel(params) {
    return request({
        url: '/system/specification/materialCustomImportExcel',
        method: 'post',
        data: params,
        headers: {
            'Content-Type': 'multipart/form-data'
          },
    })
}
//拆旧物资清册 自定义库  
export function listByUserAndKeywords(params) {
    return request({
        url: '/system/specification/listByUserAndKeywords',
        method: 'get',
        params
    })
}
//拆旧物资清册 自定义库 删除功能 
export function removeMaterialCustomByIds(params) {
    return request({
        url: '/system/specification/removeMaterialCustomByIds',
        method: 'post',
        data:params
    })
}
//拆旧物资清册 保存
export function generatorCjwzmxReport(params) {
    return request({
        url: '/system/specification/generatorCjwzmxReport',
        method: 'post',
        data: params,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}


//利旧物资清册 物料列表
export function inventoryMaterials(data) {
    return request({
        url: '/api/inventoryMaterials/inventoryMaterials',
        method: 'post',
        data
    })
}
//利旧物资清册 保存
export function generatorLjwzmxReport(params) {
    return request({
        url: '/system/specification/generatorLjwzmxReport',
        method: 'post',
        data:  params,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
//杆型一览图 读取数据  taskId
export function getAllRodType(params) {
    return request({
        url: '/rodLineJointDraw/getAllRodType',
        method: 'get',
        params
    })
}
//基础形式一览图 读取数据  taskId
export function getBasicForm(params) {
    return request({
        url: '/rodLineJointDraw/getBasicForm',
        method: 'get',
        params
    })
}

//电缆附属设施明细表 读取数据  taskId
export function getCableTable(params) {
    return request({
        url: '/rodLineJointDraw/getCableTable',
        method: 'get',
        params
    })
}
//电缆附属设施明细表
export function generatorDlfssstjReportTwo(data) {
    return request({
        url: '/system/specification/generatorDlfssstjReportTwo',
        method: 'post',
        data
    })
}

//电缆明细表 读取数据  taskId
export function getCableDetails(params) {
    return request({
        url: '/rodLineJointDraw/getCableDetails',
        method: 'get',
        params
    })
}
//电缆明细表
export function generatorDlmxReport(data) {
    return request({
        url: '/system/specification/generatorDlmxReport',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
// 杆塔明细表 读取数据  taskId
export function getTowerDetails(params){
    return request({
        url: '/rodLineJointDraw/getTowerDetails',
        method: 'get',
        params
    })
}
// 杆塔明细表 生成excel
export function generatorGtReport(data) {
    return request({
        url: '/system/specification/generatorGtReport',
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}

//保存图纸文件到成果目录
export function saveDwgInfo(params) {
    return request({
        url: '/system/specification/saveDwgInfo',
        method: 'post',
        data:  params,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
//#endregion 成果输出

//电气一次图设计- 设备类别
// "/interval/getListByVoltage/{voltage}"
export function getListByVoltage(params) {
    return request({
        url: `/interval/getListByVoltage/${params}`,
        method: 'get',
    })
}
//电气一次图设计- 物料列表 materialstypekey voltage
export function getMaterialsproject(data) {
    return request({
        url: '/interval/getMaterialsproject',
        method: 'post',
        data: data,
    })
}
//电气一次图设计- 方案选择列表 
export function getFaxzByWlId(data) {
    return request({
        url: `/interval/getFaxzByWlId/${data}`,
        method: 'get',
    })
}
//电气一次图设计- 预览 /getTxyl/{drawId}"
export function getTxyl(data) {
    return request({
        url: `/interval/getTxyl/${data}`,
        method: 'get',
        responseType: 'blob'
    })
}
export function getJgcs(data) {
    return request({
        url: `/interval/getJgcs/${data}`,
        method: 'get',
    })
}

export function pushJghDataToZaojia(data) {
    return request({
        url: `/engineeringToZaojia/pushJghDataToZaojia/${data}`,
        method: 'get',
    })
}


//成果复用
export function getQianVersionId(wholeProcessId,stage) {
    return request({
        url: `/engineering/getQianVersionId/${wholeProcessId}/${stage}`,
        method: 'get',
    })
}
//电气一次设计图 --变压器
export function getByq(data) {
    return request({
        url: `/interval/getByq`,
        method: 'post',
        data
    })
}
//电气一次设计图 --保存
export function intervalAdd(data) {
    return request({
        url: `/interval/add`,
        method: 'post',
        data
    })
}

//电气一次设计图 --保存文件到成果目录
export function intervalAddFile(data) {
    return request({
        url: `/interval/addFile`,
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
// 读取电气一次设计图站房数据

export function readStationData(taskId, equipmentId) {
    return request({
        url: `/interval/getPrivatepropertysByEquipmentId/${taskId}/${equipmentId}`,
        method: 'get',
    })
}
export function getModuleByZFLB(taskId, equipmentId) {
    return request({
        url: `/rodLineJointDraw/getModuleByZFLB`,
        method: 'get',
        params:{taskId,equipmentId}
    })
}
// 通道内敷设位置图

export function getUnderpassLayLocation(data) {
    return request({
        url: `/rodLineJointDraw/getUnderpassLayLocation`,
        method: 'post',
        data:data
    })
}

//成果目录获取文件类型接口
export function getName(name, stage) {
    return request({
        url: `/template/getName/${name}/${stage}`,
        method: 'get',
    })
}
//成果复用
export function chengguofuyong(id) {
    return request({
        url: `/engineering/chengguofuyong/${id}`,
        method: 'get',
    })
}
//左侧成功目录打包 zip

export function chengZip(id) {
    return request({
        url: `/zip/dcgcsjcg/${id}`,
        method: 'get',
        responseType: 'blob'
    })
}

//设备增容-柱上变台方案
export function getFangAnList(params) {
    return request({
        url: `/shebeizengrong/zsbt/getFangAnList`,
        method: 'get',
        params:params
    })
}


//设备增容-柱上变台方案-型号
export function getMuKuaiList(params) {
    return request({
        url: `/shebeizengrong/zsbt/getMuKuaiList`,
        method: 'get',
        params:params
    })
}
//中间头自动匹配
export function selectCableMiddleHead(params) {
    return request({
        url: `/rodLineJointDraw/selectCableMiddleHead2`,
        method: 'get',
        params:params
    })
}

//主要设备材料清单--删除功能
export function deleteByMaterialsProjectIdAndTaskId(data) {
    return request({
        url: `/designresultsProjectMaterials/deleteByMaterialsProjectIdAndTaskId`,
        method: 'post',
        data:data
    })
}
//工程参数设置-回显
export function getEngineeringSetting(params) {
    return request({
        url: `/EngineeringParameterManagement/getEngineeringSetting`,
        method: 'get',
        params:params
    })
}
//工程参数设置-保存
export function engineeringSettingSetUp(params) {
    return request({
        url: `/EngineeringParameterManagement/EngineeringSettingSetUp`,
        method: 'post',
        data:params
    })
}
export function designFilePart(params) {
    return request({
        url: `/designFilePart/saveFileFenpian`,
        method: 'post',
        headers: {
            "Content-Type": "multipart/form-data",
        },
        data:params
    })
}

export function getAnnotateAuxiliaryEquipment(data) {
    return request({
        url: `/labelManagement/getAnnotateAuxiliaryEquipment`,
        method: 'post',
        data:data
    })
}

//站房增容-类型

export function zflx(params) {
    return request({
        url: `/shebeizengrong/zflx`,
        method: 'get',
        params:params
    })
}
//站房增容-型号
export function zfxh(params) {
    return request({
        url: `/shebeizengrong/zfxh`,
        method: 'post',
        data:params
    })
}
//柱上增容-容量
export function zfrl(params) {
    return request({
        url: `/shebeizengrong/zfrl`,
        method: 'get',
        params:params
    })
}