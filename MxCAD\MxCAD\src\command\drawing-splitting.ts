import {
    MxCpp,
    MxCADUiPr<PERSON>ey<PERSON>ord,
    MxCADUiPrEntity,
    MxCADSelectionSet,
    MxCADResbuf,
    MxCADUtility,
    McObjectId,
    MxCADUiPrPoint,
    McDbBlockReference,
    McObject,
    McDbDatabase
} from "mxcad";
import { MxFun} from "mxdraw";


async function Mx_DrawingSplitting(data: any) {
    console.log('Mx_DrawingSplitting')

    // MxFun.sendStringToExecute('Mx_Export_DWG');

    const list = data.params.data
    console.log('list', list)
    /*const ss = new MxCADSelectionSet();
    let filter = new MxCADResbuf();
    filter.AddMcDbEntityTypes("INSERT");
    //选择所有图形元素
    ss.allSelect(filter);
    if (ss.count() == 0) return;*/

    const fileList = []
    const paramList = []
    list.forEach((item) => {
        // 通过句柄查找对象
        const handle = item.handle
        
        
        console.log('handle', handle)
        const dataBase: McDbDatabase = MxCpp.getCurrentMxCAD().getDatabase();
        console.log('dataBase', dataBase)
        const objectId = dataBase.handleToIdIndex(handle);
        console.log('objectId', objectId);
        const ent = objectId.getMcDbEntity();
        console.log('entity', ent);
        if(ent){
            console.log(ent,'选中的ent1111111111');
            console.log(item,'iiiiiiiiiiiiiiiiiiii');
            let equipmentInfo = null
            if(item.equipmentInfo) {
                equipmentInfo = JSON.parse(item.equipmentInfo);
            }
            const tzmc = equipmentInfo?.tzmc;
            console.log("目标实体对象",ent);
            const { minPt, maxPt, ret } = ent.getBoundingBox()
            console.log('minPt', minPt, maxPt, ret)

            let param = {
                bd_pt1_x: "" + minPt.x,
                bd_pt1_y: "" + minPt.y,
                bd_pt2_x: "" + maxPt.x,
                bd_pt2_y: "" + maxPt.y,
                id:item.id,
                tzmc:tzmc
                // ...equipmentInfo
            };
            console.log('param', param)

            const mxcad:McObject = MxCpp.App.getCurrentMxCAD()
            // console.log('fileName', tzmc)

            mxcad.saveFile(void 0, (data) => {
                let blob: Blob;
                const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                if (isSafari) {
                    blob = new Blob([data.buffer], { type: "application/octet-stream" });
                } else {
                    blob = new Blob([data.buffer], { type: "application/octet-binary" });
                }
                const file = new File([blob], `${tzmc}.mxweb`, { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
                if(!(minPt.x==0&&minPt.y==0&&maxPt.x==0&&maxPt.y==0)){
                    fileList.push(file);
                    paramList.push(param);
                }
              
            }, false, true);

        }
    })
/*    return
    ss.forEach((idF)=> {
        console.log('idF', idF)
        const item = list.find(item => item.equipmentId == idF.id);
        // if (item) {
            const ent = idF.getMcDbEntity() as McDbBlockReference;
            console.log('ent', ent)
            const {x, y, z} = ent.position;
        const { minPt, maxPt, ret } = ent.getBoundingBox()
        console.log('minPt', minPt, maxPt, ret)

        const mxcad:McObject = MxCpp.App.getCurrentMxCAD()
        const fileName =  mxcad.getCurrentFileName()
        console.log('fileName', fileName)
        mxcad.saveFile(fileName, (data)=> {
            let blob;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            if (isSafari) {
                blob = new Blob([data.buffer], { type: "application/octet-stream" });
            } else {
                blob = new Blob([data.buffer], { type: "application/octet-binary" });
            }
            const file = new File([blob], fileName + '.mxweb', { type: isSafari ? "application/octet-stream" : "application/octet-binary" });
            fileList.push(file);
        }, false, false, minPt)
        // console.log('blob',blob)
        // }
    })*/
    console.log('fileList', fileList)
    const msg = {
        messageId: data?.id,
        params: {
            fileList,
            paramList
        }
    }
    MxFun.postMessageToParentFrame({messageId: data?.id, params: msg });

}


export function init() {
    MxFun.addCommand("Mx_DrawingSplitting", Mx_DrawingSplitting);
}
