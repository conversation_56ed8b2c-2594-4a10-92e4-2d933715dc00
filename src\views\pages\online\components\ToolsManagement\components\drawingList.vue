<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { addTuZhiMuLu, getTuZhiMuLuList, legendTypesList, updateTuZhi } from "@/api/desginManage/ToolManagement.js";

import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { useRoute } from "vue-router";
import { inject } from "vue";
import { useCAdApp } from "@/hooks/useCAdApp.js";
import { upDateFunList, zdppUpdate } from "@/utils/dwgUpdate.js"
import publicStore from "../../../../../../store/modules/publicStore";
const callChildC = inject('callChildC');
let { proxy } = getCurrentInstance();
const route = useRoute();
const projectInfoStore = useProjectInfoStore();
const appStore = useAppStore();
const stage = projectInfoStore.getProjectInfo('stage')
const projectName = projectInfoStore.getProjectInfo('projectName')
const sortSelect = ref(null)
const { cadAppSrc } = useCAdApp()

const cadIframeSrc = cadAppSrc({
  isPreview: '1'
})

const cadIframeRef = ref(null)
const oldList = ref([])
console.log(projectName, stage)
const form = ref({
  gcmc: '',
  sh: '',
  sjjd: '',
  zyfzr: '',
  jcbhJ: null,
  jcbhC: null,
  sp: '',
  zymc: '',
  jh: '',
  jcmc: '',
  jcfzr: '',
  thQz: '',
  thQsz: null,
  thHz: '',
  jcsyh: '',
})
const sortConfig = {
  defaultSort: {
    field: 'sort',
    order: 'asc'
  },
}
const closeDialog = () => {
  appStore.mapIndex = "";
};
const input = ref('')
const tableData = ref([])
// 处理点击事件
const oniframeMessageF = (param) => {
  // const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  const myiframe = cadIframeRef.value
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessageF);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessageF);
  }
}
const handleMessageF = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '图纸目录File') {
      const files = event.data.params.formData.files
      console.log(files, 'filesfiles')
      const fileFormData = new FormData()
      files.forEach(item => {
        fileFormData.append('files', item)
      })
      fileFormData.append('taskId', route.query.id)
      fileFormData.append('stage', route.query.stage)
      addTuZhiMuLu(fileFormData).then(res => {
        if (res.code === 200) {
          proxy.$message.success("操作成功");
        } else {
          proxy.$message.error(res.msg);
        }
        if (callChildC) {
          callChildC();
        }
      })
    }
  }
}
const configBtn = () => {
  oniframeMessageF({
    type: "MsgCad",
    content: "Mx_drawingList",
    formData: {
      arrList: JSON.stringify([
        { name: '卷册索引号', value: form.value.jcsyh },
        { name: '工程名称', value: form.value.gcmc },
        { name: '设计阶段', value: form.value.sjjd },
        { name: '审批', value: form.value.sp },
        { name: '专业名称', value: form.value.zymc },
        { name: '卷', value: form.value.jcbhJ },
        { name: '册', value: form.value.jcbhC },
        { name: '审核', value: form.value.sh },
        { name: '卷册名称', value: form.value.jcmc },
        { name: '专业负责人', value: form.value.zyfzr },
        { name: '图纸张数', value: tableData.value.length },
        { name: '说明书数', value: '0' },
        { name: '清册数', value: '0' },
        { name: '校核', value: form.value.jh },
        { name: '日期', value: min_formatDateTime(new Date, 'yyyy-MM-dd') },
        { name: '卷册负责人', value: form.value.jcfzr },
        { name: '总页数', value: Math.ceil(tableData.value.length / 23) },
        { name: '当前页', value: '1' },
      ]),
      tableData: JSON.stringify(tableData.value),
    }
  })
}
// 将date转换为yyyy-MM-dd格式
const min_formatDateTime = (dateTime, type) => {
  const year = dateTime.getFullYear();
  const month = (dateTime.getMonth() + 1).toString().padStart(2, "0");
  const day = dateTime.getDate().toString().padStart(2, "0");
  const hours = dateTime.getHours().toString().padStart(2, "0");
  const minutes = dateTime.getMinutes().toString().padStart(2, "0");
  const seconds = dateTime.getSeconds().toString().padStart(2, "0");
  if (type == 'yyyy-MM-dd') {
    return `${year}-${month}-${day}`
  } else if (type == 'yyyy-MM-dd HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else if (type == 'yyyy-MM-dd HH:mm') {
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }
}
const dataList = () => {
  getTuZhiMuLuList(route.query.id).then(res => {
    tableData.value = res.data.sort((item, item2) => {
      return item.sort - item2.sort
    })
  })
}
const rowDragConfig = {
  disabledMethod({ row }) {
    // if (!row.id) {
    //   return true
    // }
    // return false
    return false
  }
}
const thBtnReset = () => {
  form.value.thQz = ''
  form.value.thQsz = ''
  form.value.thHz = ''
  tableData.value = oldList.value
  console.log(tableData.value);


}
const thBtnConfig = () => {
  const list = tableData.value.filter(item => item.id)
  oldList.value = JSON.parse(JSON.stringify(tableData.value.filter(item => item.id)))
  let num = form.value.thQsz
  list.forEach(item => {
    if (item.th && item.th !== "" && item.th !== null) {
      item.th = form.value.thQz + num + form.value.thHz
    }
    num++
  })

}
const tzgxBtn = () => {
  const list = tableData.value.filter(item => item.id)
  const crtkList = []
  let zdppList = []
  console.log('tzgxBtnlist', list);
  updateTuZhi(list).then(async res => {
    console.log(upDateFunList);
    try {
      if (res.code === 200) {
        for (const item of list) {
          for (const element of upDateFunList) {
            if (item.tm == element.tm) {
              await element.fun(item.th)
            }
          }
        }
        for (const item of list) {
          for (const element of upDateFunList) {
            
            if (JSON.parse(item.equipmentInfo).tklx && element.fun2) {
              console.log('crtkList',JSON.parse(item.equipmentInfo));
              // crtkList.push(item)
              alert(item.th)
              await element?.fun2(item.th, item)
            }
            
          }
        }
        zdppList = list.filter(item=>{
          console.log(JSON.parse(item.equipmentInfo).drawingid)
            return JSON.parse(item.equipmentInfo).drawingid
        })
        console.log(zdppList)
        if (zdppList.length > 0) {
        zdppUpdate(zdppList)
      }
        proxy.$message.success("操作成功");
        setTimeout(() => {
          callChildC()
        }, 2000);
      }
    } catch (error) {
      console.log(error)
      proxy.$message.error(error);
    } 
      dataList()
  })
}
const rowDragstart = (even) => {
  console.log(even);
  for (const item of tableData.value) {
    if (even.row.id == item.id) {
      sortSelect.value = even.row
    }
  }
}
const rowDragend = async (even) => {
  for (const item of tableData.value) {
    if (item.id == sortSelect.value.id) {
      item.sort = even._index.newIndex
    }
  }
  for (const item of tableData.value) {
    if (item.id == even.newRow.id) {
      item.sort = even._index.oldIndex
    }
  }
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "图纸目录") {
      form.value.gcmc = projectName
      form.value.sjjd = stage === "1" ? "需求" : stage === "2" ? "可研" : stage === "3" ? "初设" : stage === "4" ? "施工" : stage === "5" ? "施工变更" : stage === "5" ? "竣工" : ""
      dataList()
    }
  }
);
</script>

<template>
  <!--  图纸目录-->
  <data-dialog v-if="appStore.mapIndex == '图纸目录'" dataWidth="830px" @close="closeDialog">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        图纸目录
      </h4>
    </template>
    <template #body>
      <div class="formBox">
        <div class="formBox-item">
          <span> 卷册索引号 </span>
        </div>
        <div class="formBox-input" style="width: 280px">
          <el-input v-model="form.jcsyh" class="in-item" placeholder="" style="width: 270px" />
        </div>
        <div class="formBox-btn">
          <el-button round type="primary" @click="tzgxBtn">图纸更新</el-button>
        </div>
      </div>
      <div class="item_title">
        <div class="icon"></div>
        <span>图纸目录</span>
      </div>
      <div style="display: flex">
        <div class="formBox">
          <div class="formBox-item">
            <span> 工程名称 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.gcmc" class="in-item" placeholder="" readonly />
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item">
            <span> 审核 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.sh" class="in-item" placeholder="" />
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="formBox">
          <div class="formBox-item">
            <span> 设计阶段 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.sjjd" class="in-item" placeholder="" readonly />
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item">
            <span> 专业负责人 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.zyfzr" class="in-item" placeholder="" />
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="formBox">
          <div class="formBox-item">
            <span> 卷册编号 </span>
          </div>
          <div class="formBox-input">
            <div style="display: flex;align-items: center">
              <div style="display: flex;align-items: center;margin-right: 10px">
                <span class="text-box">第</span>
                <el-input-number v-model="form.jcbhJ" :controls="false" style="width: 50px" />
                <span class="text-box">卷</span>
              </div>
              <div style="display: flex;align-items: center">
                <span class="text-box">第</span>
                <el-input-number v-model="form.jcbhC" :controls="false" style="width: 50px" />
                <span class="text-box">册</span>
              </div>
            </div>
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item">
            <span> 审批 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.sp" class="in-item" placeholder="" />
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="formBox">
          <div class="formBox-item">
            <span> 专业名称 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.zymc" class="in-item" placeholder="" />
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item">
            <span> 校核 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.jh" class="in-item" placeholder="" />
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="formBox">
          <div class="formBox-item">
            <span> 卷册名称 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.jcmc" class="in-item" placeholder="" />
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item">
            <span> 卷册负责人 </span>
          </div>
          <div class="formBox-input">
            <el-input v-model="form.jcfzr" class="in-item" placeholder="" />
          </div>
        </div>
      </div>
      <div class="item_title">
        <div class="icon"></div>
        <span>图号调整</span>
      </div>
      <div style="display: flex; align-items: center;">
        <div class="formBox">
          <div class="formBox-item" style="width: 60px">
            <span> 前缀 </span>
          </div>
          <div class="formBox-input" style="width: 80px">
            <el-input v-model="form.thQz" placeholder="" style="width: 70px" />
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item" style="width: 70px">
            <span> 起始值 </span>
          </div>
          <div class="formBox-input" style="width: 80px">
            <el-input-number v-model="form.thQsz" :controls="false" style="width: 70px" />
          </div>
        </div>
        <div class="formBox">
          <div class="formBox-item" style="width: 60px">
            <span> 后缀 </span>
          </div>
          <div class="formBox-input" style="width: 80px">
            <el-input v-model="form.thHz" placeholder="" style="width: 70px" />
          </div>
        </div>
        <el-button round type="primary" @click="thBtnConfig">调整</el-button>
        <el-button round type="primary" @click="thBtnReset">还原</el-button>
      </div>
      <vxe-table ref="tableRef" :column-config="{}" :data="tableData" :header-cell-class-name="() => 'col-green'"
        :row-config="{ drag: true }" :row-drag-config="rowDragConfig" border class="mytable-style" max-height="300"
        style="width: 100%;" @row-dragend="rowDragend" @row-dragstart="rowDragstart" :sort-config="sortConfig">
        <vxe-column type="seq" width="55"></vxe-column>
        <vxe-column field="zl" title="种类" width="80">
          <template #default="{ row }">
            <el-input v-if="row.id" v-model="row.zl" placeholder="" disabled />
            <span v-else>{{ row.zl }}</span>
          </template>
        </vxe-column>
        <vxe-column field="th" title="图号" width="100">
          <template #default="{ row }">
            <el-input v-if="row.id" v-model="row.th" placeholder="" />
            <span v-else>{{ row.th }}</span>
          </template>
        </vxe-column>
        <vxe-column field="tm" title="图名" width="200">
          <template #default="{ row }">
            <el-input v-if="row.id" v-model="row.tm" placeholder="" disabled />
            <span v-else>{{ row.tm }}</span>
          </template>
        </vxe-column>
        <vxe-column field="ty" title="套用工程名称及图号" width="160">
          <template #default="{ row }">
            <el-input v-if="row.id" v-model="row.ty" placeholder="" />
            <span v-else>{{ row.ty }}</span>
          </template>
        </vxe-column>
        <vxe-column drag-sort title="顺序调整" width="80"></vxe-column>
      </vxe-table>
      <div class="line" style="background: #fff; height: 50px">
        <div class="line-bnt" @click="configBtn">确定</div>
      </div>
      <iframe v-show="false" ref="cadIframeRef" :src="cadIframeSrc" />
    </template>
  </data-dialog>
</template>

<style lang="scss" scoped>
@use '../../index' as *;

.item_title {
  margin: 5px 0px 5px 2px;
  display: flex;
  font-weight: 700;
  align-items: center;
  color: #50adaa;
  font-size: 15px;

  .icon {
    margin-right: 6px;
    width: 3px;
    height: 18px;
    background-color: #50adaa;
  }
}

::v-deep(.mytable-style.vxe-table .vxe-header--column.col-green) {
  background: #50a9aa;
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}

.text-box {
  font-weight: normal;
}
</style>
