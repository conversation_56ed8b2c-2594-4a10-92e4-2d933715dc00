
import useAppStore from "@/store/modules/app.js";
import cjwzDwgUpdate from "@/store/modules/dwgUpdate/cjwzDwgUpdate"
import zyclqdDwgUpdate from "@/store/modules/dwgUpdate/zyclqdDwgUpdate"
import dlmxqdDwgUpdate from "@/store/modules/dwgUpdate/dlmxDwgUpdate"
import ggmxDwgUpdate from "@/store/modules/dwgUpdate/ggmxDwgUpdate"
import ljwzDwgUpdate from "@/store/modules/dwgUpdate/ljwzDwgUpdate"
import crtkDwgUpdate from "@/store/modules/dwgUpdate/crtkDwgUpdate"
import dltdtjDwgUpdate from "@/store/modules/dwgUpdate/dltdtjDwgUpdate"
import jcylDwgUpdate from "@/store/modules/dwgUpdate/jcylDwgUpdate"
import gxylDwgUpdate from "@/store/modules/dwgUpdate/gxylDwgUpdate"
import { changeEngineeringVersion, getHzgctuByTaskId } from "@/api/desginManage/GccsDialog";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { getBaseSettingList } from "@/api/taskBaseData/index.js";
import { ref } from "vue"
import { ElLoading } from 'element-plus'
import { ElMessage, ElMessageBox } from "element-plus";
import { sdkUndo, sdkRedo, sdkRemoveDevice } from "@/views/pages/online/sdkDrawBackData/baseSDK"
import { saveDwgInfo, generatorDltdtjReport, generatorGtReport, getAnnotateAuxiliaryEquipment } from "@/api/insertSag/index.js"
import { undoEquipmentInfoByFlag } from "@/api/desginManage/draw.js"
import {
    addEquipmentInfo,
} from "@/api/desginManage/ToolManagement.js";
import {
    addDrawingMuLu
} from "@/api/desginManage/ToolManagement.js";
const appStore = useAppStore();
const { sendMessage, cleanup } = useIframeCommunication()
const publicStore = defineStore(
    'publicStore',
    {
        state: () => ({
            childRef: undefined, //目录ref
            taskId: undefined,
            myiframehide: undefined,
            iframe: undefined,
            crtktzid: undefined
        }),
        actions: {
            oniframeMessage(param) {
                try {
                    console.log('param', param);
                    const myiframe = useAppStore().iframeHide;
                    if (myiframe && myiframe.contentWindow) {
                        // 先移除之前的事件监听器，防止重复绑定
                        async function handleMessage(event) {

                            console.log('handleMessageevent', event);
                            if (event.data.params.content == '拆旧物资清册') {
                                await cjwzDwgUpdate().message(event)
                            }
                            if (event.data.type === 'parentCad' && event.data.params.content === '电缆明细表') {
                                await dlmxqdDwgUpdate().message(event)
                            }
                            if (
                                event.data.type === "parentCad" &&
                                event.data.params.content === "材料清单生成报表"
                            ) {
                                await zyclqdDwgUpdate().message(event)
                            }
                            if (event.data.params.content === '钢管杆明细表') {
                                await ggmxDwgUpdate().message(event)
                            }
                            if (event.data.type === 'parentCad' && event.data.params.content === '利旧物资表') {
                                await ljwzDwgUpdate().message(event)
                            }
                            if (event.data.type === 'parentCad') {
                                if (event.data.params.content === '插入图框') {
                                    console.log('handleMessageeventparentCad', event);
                                    delete form.value.tklxOption
                                    delete form.value.tzblOption
                                    delete form.value.tzlxOption
                                    form.value.tzlxName = options.value.optionTzlx.find(item => item.id === form.value.tzlx).name;
                                    form.value.tfdxName = options.value.optionTfdx.find(item => item.id === form.value.tfdx).name;
                                    await addEquipmentInfo({ 'prj_task_info_id': new URLSearchParams(new URL(window.location.href).search).get('id'), handle: event.data.params.handle, 'equipment_info': JSON.stringify(form.value) })
                                }
                            }
                            if (event.data.type === 'parentCad') {
                                if (event.data.params.content === '自动匹配图纸带有图框图签') {
                                    // alert(event.data.params.content)
                                    const files = event.data.params.formData.files
                                    // console.log(files, 'filesfiles')
                                    // const fileFormData = new FormData()
                                    // files.forEach(item => {
                                    //     fileFormData.append('files', item.file)
                                    // })
                                    // fileFormData.append('taskId', new URLSearchParams(new URL(window.location.href).search).get('id'))
                                    // fileFormData.append('stage', new URLSearchParams(new URL(window.location.href).search).get('stage'))
                                    const queryList = []
                                    for (const item of files) {
                                        console.log('queryListitem', item);

                                        let content;
                                        await new Promise((resolve, reject) => {
                                            const fileReader = new FileReader();
                                            const fileRes = fileReader.readAsDataURL(item.file)
                                            fileReader.onload = function () {
                                                content = fileReader.result
                                                console.log(content)
                                                if (content) {
                                                    resolve()
                                                }
                                            }
                                        })
                                        queryList.push({
                                            content: content,
                                            name: item.file.name,
                                            taskId: new URLSearchParams(new URL(window.location.href).search).get('id'),
                                            stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                                            equipmentInfos: JSON.stringify({ drawingid: item.drawingid, th: item.th, sort: item.sort })
                                        })
                                    }
                                    await addDrawingMuLu(queryList)
                                }
                            }

                            // if (event.data.type === 'cadPreview' && event.data.params.content === 'Mx_zdTzfileTk') {
                            //     await zdpptzDwgUpdate().message(event)
                            // }

                            // if (event.data.params.content === "DLTDTJ") {
                            //     await dltdtjDwgUpdate().message(event)
                            // }
                            // if (event.data.params.content === "JCYL") {
                            //     await jcylDwgUpdate().message(event)
                            // }
                            // if (event.data.params.content === 'GXYL') {
                            //     await gxylDwgUpdate().message(event)
                            // }
                        }
                        window.removeEventListener("message", handleMessage);
                        // 添加新的事件监听器
                        window.addEventListener("message", handleMessage);
                        myiframe.contentWindow.postMessage(param, "*");
                    }
                } catch (error) {
                    console.log('errordddd', error);

                    reject(error)
                }

            },

            postMessageMsgCadReport(item, data, fileBlobArr) {
                this.myiframehide.contentWindow.postMessage({
                    type: item.type,
                    content: item.content,
                    formData: {
                        fileBlobArr,
                        tableData: JSON.stringify(data),
                        countyOrganisationName: new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName'),
                        projectName: new URLSearchParams(new URL(window.location.href).search).get('projectName'),
                        stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                        proCode: new URLSearchParams(new URL(window.location.href).search).get('proCode'),
                        proId: new URLSearchParams(new URL(window.location.href).search).get('id'),
                        content: item.content,
                        offsetX: 0,
                        th: item.th
                    },
                },
                    "*"
                );
            },
            async onlineDesignMessage(event) {
                console.log('onlineDesign接收', event)
                if (!event) return
                const baseSettingData = ref([])
                const getBaseSetting = async () => {
                    const res = await getBaseSettingList({ taskId: this.taskId })
                    baseSettingData.value = res.data
                }
                getBaseSetting()
                const drawSettingList = () => {
                    const { drawSetting } = baseSettingData.value
                    if (!drawSetting) return []
                    const drawData = JSON.parse(drawSetting)
                    const list = []
                    drawData.forEach(item => {
                        list.push(
                            ...item.SettingList,
                        )
                    })
                    return list
                }
                const initHzgctu = (data) => {
                    const list = data.map(item => {
                        const matchedSetting = drawSettingList().find(setting => setting.LegendTypeKey === item.legendtypekey && setting.LegendState === item.state);
                        return {
                            ...item,
                            drawStyle: matchedSetting ? matchedSetting : null,
                            content: 'cad'
                        };
                    })
                    setTimeout(() => {
                        sendMessage(this.iframe, { type: 'Huzhi', content: list })
                    }, 1000)
                }
                if (event.data.params?.type && event.data.params?.type == "Hzgctu") {
                    getHzgctuByTaskId(this.taskId).then((res) => {
                        initHzgctu(res.data)
                    })
                }
                if (import.meta.env.VITE_APP_MX_APP_SRC.indexOf(event.origin) < 0) return

                const { type, params, cmd } = event.data?.params
                console.log(event.data?.params, 'dddd')
                if (event.data.type === 'click') {
                    console.log(event.data.params.selectObjs, '走这个')
                    let propertyList = event.data.params.selectObjs
                    appStore.setPropertyInfo(propertyList)
                    appStore.setSdkClassName(event.data.params)
                    //初始化通道内电缆
                    appStore.setCable([])
                    //获取通道内电缆信息
                    if (event.data.params?.clickItemClassName == "CableChannelSec") {
                        sendMessage(appStore.iframe, { type: 'greeting', content: 'SDK_tongdao', options: {} }, (res1) => {
                            console.log("🚀 ~ sendMessage ~ res1:", res1)
                            //赋值通道内电缆
                            appStore.setCable(res1.params.result)
                        });
                    }
                } else if (event.data.type === "parentCad") {
                    if (event.data.params.content === "GXYL" || event.data.params.content === "JCYL") {
                        const loading = !event.data.params.formData.th ? ElLoading.service({
                            lock: true,
                            text: '报表正在生成中，请稍等...',
                            background: 'rgba(0, 0, 0, 0.7)',
                        }) : null
                        //保存报表文件到成果目录
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('files', item)// multipartFile
                        })
                        fileFormData.append("name", event.data.params.content === "GXYL" ? "杆塔型式一览图" : "基础型式一览图");
                        fileFormData.append('taskId', event.data.params.formData.proId)
                        fileFormData.append('stage', event.data.params.formData.stage)
                        fileFormData.append('time', new Date())
                        const res = await saveDwgInfo(fileFormData)
                        if (res.code === 200) {
                            !event.data.params.formData.th && ElMessage.success("保存成功");
                            !event.data.params.formData.th && loading.close()
                            if (!event.data.params.formData.th) {
                                // callChildC();
                                this.childRef.MethodB();
                            }
                        } else {
                            ElMessage.error(res.msg);
                            loading.close()
                        }
                    } else if (event.data.params.content === "DLTDTJ") {
                        const loading = !event.data.params.formData.th ? ElLoading.service({
                            lock: true,
                            text: '报表正在生成中，请稍等...',
                            background: 'rgba(0, 0, 0, 0.7)',
                        }) : null
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const tableData = JSON.parse(event.data.params.formData.tableData)
                        const tableDataNew = tableData.map((item, index) => {
                            return {
                                xh: index + 1,
                                lx: item.moduleName,
                                gg: item.moduleType,
                                mk: item.moduleCode,
                                dw: item.unit,
                                sl: item.quantity,
                                zt: item.state,
                                bz: ''
                            }
                        })
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('multipartFile', item)
                        })
                        fileFormData.append("dltdtjList", JSON.stringify(tableDataNew));
                        fileFormData.append('prjTaskInfoId', event.data.params.formData.proId)
                        fileFormData.append('stage', event.data.params.formData.stage)
                        fileFormData.append('time', new Date())
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        const res = await generatorDltdtjReport(fileFormData)
                        if (res.code === 200) {
                            !event.data.params.formData.th && ElMessage.success("保存成功");
                            !event.data.params.formData.th && loading.close()
                            if (!event.data.params.formData.th) {
                                // callChildC();
                                this.childRef.MethodB();
                            }
                        } else {
                            ElMessage.error(res.msg);
                            loading.close()
                        }
                    } else if (event.data.params.content === "GTMX") {
                        const loading = !event.data.params.formData.th ? ElLoading.service({
                            lock: true,
                            text: '报表正在生成中，请稍等...',
                            background: 'rgba(0, 0, 0, 0.7)',
                        }) : null
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const tableData = JSON.parse(event.data.params.formData.tableData)
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('multipartFile', item)
                        })
                        fileFormData.append("gtList", JSON.stringify(tableData));
                        fileFormData.append('prjTaskInfoId', event.data.params.formData.proId)
                        fileFormData.append('stage', event.data.params.formData.stage)
                        fileFormData.append('time', new Date())
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        const res = await generatorGtReport(fileFormData)
                        if (res.code === 200) {
                            !event.data.params.formData.th && ElMessage.success("保存成功");
                            !event.data.params.formData.th && loading.close()
                            if (!event.data.params.formData.th) {
                                // callChildC();
                                this.childRef.MethodB();
                            }
                        } else {
                            ElMessage.error(res.msg);
                            !event.data.params.formData.th && loading.close()
                        }
                    }
                } else if (event.data.type === "SNG_BZ") {
                    console.log("获取水泥杆制品数据", event.data.params)
                    let SNGArr = []
                    event.data.params.idList.forEach((item) => {
                        SNGArr.push(item.equipmentId)
                    })
                    let paramsSNG = {
                        taskId: this.taskId,
                        equipmentIds: SNGArr
                    }
                    const result = await getAnnotateAuxiliaryEquipment(paramsSNG)
                    console.log(result, 'ddddddddddd')
                    let options = { name: 'JGFZH_Draw', tableData: JSON.stringify(result.data) }
                    sendMessage(appStore.iframe, { type: 'onlycad', content: 'Marking_OfCementPole_Products', options }, (res1) => {
                        console.log(res1, 'ssss')
                    });
                    // getAnnotateAuxiliaryEquipment(paramsSNG).then(result => {

                    // })
                }
                // 提示消息
                if (type === "showMessage") {
                    if (params.msgType === 'success') {
                        ElMessage.success(params.content)
                    } else if (params.msgType === 'error') {
                        ElMessage.error(params.content)
                    } else if (params.content) {
                        ElMessage.warning(params.content)
                    }
                } else if (type === "saveEngineeringVersion") {
                    //用户保存工程版本
                    changeEngineeringVersion(params).then((res) => {
                        if (res.code === 200) {
                            ElMessage.success("已保存工程版本")
                            // 获取当前URL对象
                            const currentUrl = new URL(window.location.href);
                            const currentParams = new URLSearchParams(currentUrl.search);

                            // 处理startFlag逻辑
                            if (currentParams.get('startFlag')) {
                                proxy.$modal.loading("图模复用中，请稍候...");
                                const options = {
                                    extendVersionId: currentParams.get('oldVersionId'),
                                };

                                sendMessage(appStore.iframe, { type: 'greeting', content: 'SDK_versionExtend', options }, (res1) => {
                                    if (res1.cmd === "SDK_versionExtend" && res1.params.status === "000000") {
                                        proxy.$message.success(res1.params.message);
                                        proxy.$modal.closeLoading();

                                        // 删除参数
                                        currentParams.delete('startFlag');
                                        currentParams.delete('oldVersionId');

                                        // 更新URL
                                        const newSearch = currentParams.toString();
                                        const newUrl = newSearch
                                            ? `${currentUrl.pathname}?${newSearch}${currentUrl.hash}`
                                            : `${currentUrl.pathname}${currentUrl.hash}`;

                                        window.history.replaceState({}, '', newUrl);
                                    }
                                });
                            }
                        } else {
                            ElMessage.success("工程版本保存失败")
                        }
                        console.log('保存工程版本', res)
                    })
                } else if (type === "reply") {
                    console.log('onlineDesign', type, cmd, params)

                    if (cmd === 'SDK_undo') {
                        sdkUndo(params)
                    } else if (cmd === 'SDK_redo') {
                        sdkRedo(params)
                    } else if (cmd === 'SDK_removeDevice') {
                        sdkRemoveDevice(params)
                    }
                    if (cmd == 'cad_removeDevice') {
                        console.log(params);

                        const deleteMachineParams = params.map(item => {
                            return {
                                ...item,
                                taskId: this.taskId
                            }
                        })
                        undoEquipmentInfoByFlag(deleteMachineParams).then(res => {
                            if (res.code == 200) {
                                ElMessage.success('删除成功')
                            }
                        })
                        // getHzgctuByTaskId(taskId).then((res)=>{
                        //     initHzgctu(res.data)
                        //   })
                    }
                    if (params?.status === "000000") {

                    }
                }
            }
        },
        persist: true
    })
export default publicStore
