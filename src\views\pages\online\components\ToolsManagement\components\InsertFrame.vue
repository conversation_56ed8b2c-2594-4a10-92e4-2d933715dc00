<template>
<!-- 插入图框 -->
  <data-dialog
      @close="closeDialog"
      dataWidth="830px"
      v-if="appStore.mapIndex == '插入图框'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        插入图框
      </h4>
    </template>
    <template #body>
      <div style="display: flex">
        <div class="lineTwo">
          <div class="line-item">
            <span> 设计单位 </span>
          </div>
          <div class="line-input">
            <el-input class="in-item" v-model="form.sjdw" placeholder="" readonly/>
          </div>
        </div>
        <div class="lineTwo">
          <div class="line-item">
            <span> 设计阶段 </span>
          </div>
          <div class="line-input">
            <el-input class="in-item" v-model="form.sjjd" placeholder="" readonly/>
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="lineTwo">
          <div class="line-item">
            <span> 工程名称 </span>
          </div>
          <div class="line-input">
            <el-input class="in-item" v-model="form.gcmc" placeholder="" readonly/>
          </div>
        </div>
        <div class="lineTwo">
          <div class="line-item">
            <span> 日期 </span>
          </div>
          <div class="line-input">
            <div class="in-item">
              <el-date-picker
                  v-model="form.time"
                  type="date"
                  placeholder="年/月/日"
              />
            </div>
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="lineTwo">
          <div class="line-item">
            <span> 图纸类型 </span>
          </div>
          <div class="line-input">
            <el-select
                class="in-item"
                v-model="form.tzlx"
                placeholder="请选择"
                @change="changeTzlx"
            >
              <el-option
                  v-for="item in options.optionTzlx"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="lineTwo">
          <div class="line-item">
            <span> 图纸名称 </span>
          </div>
          <div class="line-input">
            <el-input class="in-item" v-model="form.tzmc" placeholder=""/>
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="lineTwo">
          <div class="line-item">
            <span> 图纸比例 </span>
          </div>
          <div class="line-input">
            <el-select
                class="in-item"
                v-model="form.tzbl"
                placeholder="请选择"
            >
              <el-option
                  v-for="item in options.optionTzbl"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="lineTwo">
          <div class="line-item">
            <span> 图号 </span>
          </div>
          <div class="line-input">
            <el-input class="in-item" v-model="form.th" placeholder=""/>
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div class="lineTwo">
          <div class="line-item">
            <span> 图框类型 </span>
          </div>
          <div class="line-input">
            <el-select
                class="in-item"
                v-model="form.tklx"
                placeholder="请选择"
                @change="tklxTotfdx"
            >
              <el-option
                  v-for="item in options.optionTklx"
                  :key="item"
                  :label="item"
                  :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="lineTwo">
          <div class="line-item">
            <span> 图幅大小 </span>
          </div>
          <div class="line-input">
            <el-select
                class="in-item"
                v-model="form.tfdx"
                placeholder="请选择"
                @change="mxPreview"
            >
              <el-option
                  v-for="item in options.optionTfdx"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <el-tabs type="card" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="插入图框"></el-tab-pane>
        <el-tab-pane label="插入布局框"></el-tab-pane>
        <div style="height: 250px;width: 530px;">
          <!-- <div id="myiframeYl" style="height: 250px;padding: 5px"></div> -->
          <iframe
                ref="cadIframeRef"
                id="myiframeYlCrtk"
                :src="cadIframeSrc"
                style="width: 530px; height: 250px"
                v-show="activeName==0"
              />
        </div>
      </el-tabs>
      <div style="background: #fff; height: 50px" class="line">
        <div class="line-bnt" @click="CrInsertFrame">插入图纸</div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import { Drawing } from "@/utils/ThreeLoad.js"
import useAppStore from "@/store/modules/app.js";
import { useCAdApp } from "@/hooks/useCAdApp.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
const { sendMessage, cleanup } = useIframeCommunication();
const cadIframeRef = ref(null);
const { cadAppSrc } = useCAdApp();
const cadIframeSrc = cadAppSrc({
  isPreview: "1",
});
const appStore = useAppStore();
let { proxy } = getCurrentInstance();
const closeDialog = () => {
  appStore.mapIndex = "";
  const myiframe = document.getElementById("myiframeYl");
  const existingIframe = document.getElementById("MXCADYl");
  if (existingIframe) {
    myiframe.removeChild(existingIframe);
  }
};
const text = ref('')
const option = ref([])
import {tansParams} from "@/utils/ruoyi.js";
import {getToken} from "@/utils/auth.js";
import {useRoute} from "vue-router";
import {
  addEquipmentInfo,
  insertFrameCheckEnable,
  insertFrameDownloadTuKuang, insertFrameDownloadTuQian, insertFrameHuixian,
  insertFrameinitialize
} from "@/api/desginManage/ToolManagement.js";
const route = useRoute();
const urlRoute = new useRoute()
const iframe = ref(null);
const activeName=ref('')
const form = ref({
  sjdw: '',
  sjjd: '',
  gcmc: '',
  time: '',
  tzlx: '',
  tzmc: '',
  tzbl: '',
  th: '',
  tklx: '',
  tfdx: '',
  tzlxName: '',
  tfdxName: '',
})
const options = ref({
  optionTzlx: [],
  optionTzbl: [],
  optionTklx: [],
  optionTfdx: [],
})
const changeTzlx=()=>{
  form.value.tzlxName = options.value.optionTzlx.find(item => item.id === form.value.tzlx).name
  console.log(form.value.tzlxName,'dkdkdkkdkdk')
}
const cadYlFun = () => {
  const myiframe = document.getElementById("myiframeYl");
  const existingIframe = document.getElementById("MXCADYl");
  if (existingIframe) {
    myiframe.removeChild(existingIframe);
  }
  let params = tansParams({
    ...urlRoute.query,
    token: getToken() || urlRoute.query?.token,
  })
  const src = params.length !== 0
      ? `${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
      : `${import.meta.env.VITE_APP_MX_APP_SRC}`
  // const iframeElement = document.createElement("iframe");
  // iframe.value = iframeElement;
  nextTick(async () => {
    // const myiframe = document.getElementById("myiframeYl");
    // iframeElement.src = src
    // iframeElement.id = "MXCADYl";
    // iframeElement.style.width = "100%";
    // iframeElement.style.height = "100%";
    // myiframe.append(iframeElement);
    setTimeout(() => {
      const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: null,
          },
        };
        sendMessage(cadIframeRef.value, params, (res) => {});
      // oniframeMessage({
      //   type: "cadPreview",
      //   content: "Mx_cadPreview",
      //   formData: {
      //     openUrl: null,
      //   }
      // })
    },2000)
  });
}
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = document.querySelector("#myiframeYlCrtk")
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = (event) => {}
const handleClick=(tab,event)=>{
  console.log(tab,event)
  console.log(activeName.value,'activeName')
  if(activeName.value===0){
    const params = {
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: null,
          },
        };
        sendMessage(cadIframeRef.value, params, (res) => {});
  }else{
  // const params = {
  //         type: "cadPreview",
  //         content: "Mx_crbjk",
  //         formData: {type:'preview' }
  //       };
  //       sendMessage(cadIframeRef.value, params, (res) => {});
}}
// 处理点击事件
const oniframeMessageF = (param) => {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessageF);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessageF);
  }
}
const handleMessageF = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '插入图框') {
      delete form.value.tklxOption
      delete form.value.tzblOption
      delete form.value.tzlxOption
      form.value.tzlxName = options.value.optionTzlx.find(item => item.id === form.value.tzlx).name;
      form.value.tfdxName = options.value.optionTfdx.find(item => item.id === form.value.tfdx).name;
      addEquipmentInfo({ 'prj_task_info_id': route.query.id,handle: event.data.params.handle, 'equipment_info': JSON.stringify(form.value) }).then(res => {
        if (res.code === 200) {
          proxy.$message.success("操作成功");
        } else {
          proxy.$message.error(res.msg);
        }
      })
    }
  }
}

const dataList = () => {
  // 判断功能是否可用：【需求编制】设计阶段、【工程参数设置】为空禁用本功能；
  // insertFrameCheckEnable(route.query.id).then(res => {})
  //  数据库没有数据时回显默认
  insertFrameinitialize(route.query.id).then(res => {
    // 数据库有数据回显
    insertFrameHuixian(route.query.id).then(resHuixian => {
      if (resHuixian.hasOwnProperty('data')) {
        form.value = resHuixian.data
        form.value.time = resHuixian.data.time
        options.value.optionTklx = Object.keys(res.data.tklxOption);
        form.value.tklxOption=res.data.tklxOption
        options.value.optionTzbl = res.data.tzblOption
        options.value.optionTzlx = res.data.tzlxOption
        if (form.value.tklx) {
          options.value.optionTfdx = res.data.tklxOption[form.value.tklx]
          setTimeout(() => {
            mxPreview(form.value.tfdx)
          }, 3000)
        }
      } else {
        form.value = res.data
        form.value.time = min_formatDateTime(new Date(), 'yyyy-MM-dd')
        const stage = form.value.sjjd
        form.value.sjjd = stage === "1" ? "需求" : stage === "2" ? "可研" : stage === "3" ? "初设" : stage === "4" ? "施工" : stage === "5" ? "施工变更" : stage === "5" ? "竣工" : "";
        options.value.optionTklx = Object.keys(form.value.tklxOption);
        options.value.optionTzbl = form.value.tzblOption
        options.value.optionTzlx = form.value.tzlxOption
        // 设置默认值
        form.value.tklx = options.value.optionTklx.length > 0 ? options.value.optionTklx[0] : ''
        form.value.tzbl = options.value.optionTzbl.length > 0 ? options.value.optionTzbl[0].value : ''
        form.value.tzlx = options.value.optionTzlx.length > 0 ? options.value.optionTzlx[0].id : ''
        if (form.value.tklx) {
          options.value.optionTfdx = form.value.tklxOption[form.value.tklx]
          form.value.tfdx = form.value.tklxOption[form.value.tklx].length > 0 ? form.value.tklxOption[form.value.tklx][0].id : ''
          setTimeout(() => {
            mxPreview(form.value.tfdx)
          }, 3000)
        }
      }

    })
  })
}
const tklxTotfdx = (val) => {
  options.value.optionTfdx = form.value.tklxOption[val]
  form.value.tfdx = options.value.optionTfdx.length > 0 ? options.value.optionTfdx[0].id : ''
  mxPreview(form.value.tfdx)
}
const resTkBox = ref(null)
const resTqBox = ref(null)
const mxPreview = (val) => {
  insertFrameDownloadTuKuang(val).then(resTk => {
    if(resTk.type=='application/json'){
proxy.$message.warning('图框不存在');
        return;
   }
    insertFrameDownloadTuQian(route.query.id).then(resTq => {
      if(resTq.type=='application/json'){
proxy.$message.warning('图签不存在');
        return;
   }
   form.value.tzlxName = options.value.optionTzlx.find(item => item.id === form.value.tzlx).name;
      resTkBox.value = resTk
      resTqBox.value = resTq
      oniframeMessage({
        type: "cadPreview",
        content: "Mx_InsertFrame",
        formData: {
          openUrlTk: resTk,
          openUrlTq: resTq,
          sjdw: form.value.sjdw, // 单位
          gcmc: form.value.gcmc, // 项目名称
          sjjd: form.value.sjjd, // 阶段
          time: form.value.time, // 时间
          th: form.value.th, // 图号
          tzmc: form.value.tzmc, // 图纸名称
          tzbl: form.value.tzbl, // 比例
          tfdx: form.value.tfdx, // 图幅大小
          tzlx:form.value.tzlxName
        }
      })
    })
  })
}
const CrInsertFrame = () => {
  if(activeName.value==0){
    oniframeMessageF({
    type: "cadPreview",
    content: "Mx_InsertCr",
    formData: {
      openUrlTk: resTkBox.value,
      openUrlTq: resTqBox.value,
      sjdw: form.value.sjdw, // 单位
      gcmc: form.value.gcmc, // 项目名称
      sjjd: form.value.sjjd, // 阶段
      time: form.value.time, // 时间
      th: form.value.th, // 图号
      tzmc: form.value.tzmc, // 图纸名称
      tzbl: form.value.tzbl, // 比例
      tzlx:form.value.tzlxName
    }
  })
  }else{
    console.log("插入布局框")
    oniframeMessageF({
    type: "cadPreview",
    content: "Mx_crbjk",
    formData: { }
  })
  }
  
}
// 将date转换为yyyy-MM-dd格式
const min_formatDateTime = (dateTime, type) => {
  const year = dateTime.getFullYear();
  const month = (dateTime.getMonth() + 1).toString().padStart(2, "0");
  const day = dateTime.getDate().toString().padStart(2, "0");
  const hours = dateTime.getHours().toString().padStart(2, "0");
  const minutes = dateTime.getMinutes().toString().padStart(2, "0");
  const seconds = dateTime.getSeconds().toString().padStart(2, "0");
  if (type == 'yyyy-MM-dd') {
    return `${year}-${month}-${day}`
  } else if (type == 'yyyy-MM-dd HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else if (type == 'yyyy-MM-dd HH:mm') {
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
      console.log(newInfo, oldInfo);
      if (newInfo == "插入图框") {
        cadYlFun()
        dataList()
      }
    }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;
#myCanvas {
  width: 100%;
  height: 200px;
}
::v-deep .el-tabs__header {
  margin: 0 0 0px;
}
</style>