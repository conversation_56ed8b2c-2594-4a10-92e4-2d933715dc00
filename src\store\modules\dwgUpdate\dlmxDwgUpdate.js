
import {
    generatorDlmxReport, getCableDetails
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
import { getTuQianFileByPath } from "@/api/onlineDesign/index.js";
const dlmxDwgUpdate = defineStore(
    'dlmxDwgUpdate',
    {
        state: () => ({
            dlmxTableData: undefined,
        }),
        actions: {
            getDlmxData(taskId) {
                return new Promise((resolve, reject) => {
                    getCableDetails({ taskId }).then(res => {
                        try {
                            this.dlmxTableData = res.data
                            this.dlmxTableData.forEach((item, index) => {
                                item.number = index + 1;
                            });
                            resolve()
                        } catch (error) {
                            reject()
                        }

                    })
                })
            },
            async dlmxDwgUpdateFun(th) {
                publicStore().oniframeMessage({
                    type: "cadPreview",
                    content: "Mx_dlmx",
                    formData: {
                        content: "电缆明细表",
                        tableData: JSON.stringify(this.dlmxTableData),
                        countyOrganisationName: new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName'),
                        projectName: new URLSearchParams(new URL(window.location.href).search).get('projectName'),
                        stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                        proCode: new URLSearchParams(new URL(window.location.href).search).get('proCode'),
                        th: th
                    },
                });
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        const files = event.data.params.formData.files
                        const dataExcel = JSON.parse(event.data.params.formData.tableData).map(item => {
                            return {
                                xh: item.number,
                                dlmc: item.lineName,
                                dlxh: item.cableType,
                                qssbmc: item.startUserNumber,
                                zzsbmc: item.endUserNumber,
                                xjcd: item.newLength,
                                ljfs: item.oldLength,
                                zjcd: item.removeLength,
                                hwzd: item.outdoorTerminalNum,
                                hnzd: item.indoorTerminalNum,
                                sbzd: item.equipmentTerminalNum,
                                zjjt: item.midJointNum,
                                dldjx: item.cableDockingNum,
                                dlfzx: item.cableBranchNum
                            }
                        })
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('multipartFile', item)
                        })
                        fileFormData.append("dlmxList", JSON.stringify(dataExcel));
                        fileFormData.append('prjTaskInfoId', new URLSearchParams(new URL(window.location.href).search).get('id'))
                        fileFormData.append('stage', new URLSearchParams(new URL(window.location.href).search).get('stage'),)
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        console.log(dataExcel)
                        generatorDlmxReport(fileFormData).then(res => {
                            if (res.code == 200) resolve()
                            // if (res.code === 200) {
                            //     proxy.$message.success("保存成功");
                            //     loading.close()
                            //     if (callChildC) {
                            //         callChildC();
                            //     }
                            // } else {
                            //     proxy.$message.error(res.msg);
                            //     loading.close()
                            // }
                        })
                    } catch (error) {
                        reject(error)
                    }
                })

            }

        },
        persist: true
    })
export default dlmxDwgUpdate
