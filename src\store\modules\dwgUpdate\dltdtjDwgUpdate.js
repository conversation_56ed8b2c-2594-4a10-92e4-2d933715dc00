

import {
    getCableChannelDetails,
    generatorDltdtjReport
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
import { getLegendState } from "@/views/pages/online/commonData.js";
const dltdtjDwgUpdate = defineStore(
    'dltdtjDwgUpdate',
    {
        state: () => ({
            cjwzTableData: undefined,
        }),
        actions: {
            async getCableChannel(taskId) {
                return new Promise(async (resolve, reject) => {
                    try {
                        getCableChannelDetails({ projectId: publicStore().taskId }).then(res => {
                            if (res.code == 200) {
                                res.data.forEach(element => {
                                    element.state = getLegendState(element.state)
                                });
                                resolve({ data: res.data })
                            }
                        })
                    } catch (error) {
                        reject(error)
                    }

                })
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const tableData = JSON.parse(event.data.params.formData.tableData)
                        const tableDataNew = tableData.map((item, index) => {
                            return {
                                xh: index + 1,
                                lx: item.moduleName,
                                gg: item.moduleType,
                                mk: item.moduleCode,
                                dw: item.unit,
                                sl: item.quantity,
                                zt: item.state,
                                bz: ''
                            }
                        })
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('multipartFile', item)
                        })
                        fileFormData.append("dltdtjList", JSON.stringify(tableDataNew));
                        fileFormData.append('prjTaskInfoId', event.data.params.formData.proId)
                        fileFormData.append('stage', event.data.params.formData.stage)
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        generatorDltdtjReport(fileFormData).then(res => {
                            if (res.code == 200) resolve()
                            // if (res.code === 200) {
                            //     ElMessage.success("保存成功");
                            //     loading.close()
                            //     if (callChildC) {
                            //         callChildC();
                            //     }
                            // } else {
                            //     ElMessage.error(res.msg);
                            //     loading.close()
                            // }

                        })
                    } catch (error) {
                        reject(error)
                    }

                })

            }

        },
        persist: true
    })
export default dltdtjDwgUpdate
