
import publicStore from "@/store/modules/publicStore.js";
import useAppStore from "@/store/modules/app.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import {
    insertFrameDownloadTuKuang, insertFrameDownloadTuQian,
} from "@/api/desginManage/ToolManagement.js";
import {
    autoMatchingPreview,
} from "@/api/desginManage/ToolManagement.js";
const projectInfoStore = useProjectInfoStore();
const appStore = useAppStore();
const zdppDwgUpdate = defineStore(
    'zdppDwgUpdate',
    {
        state: () => ({

        }),
        actions: {
            min_formatDateTime(dateTime, type) {
                const year = dateTime.getFullYear();
                const month = (dateTime.getMonth() + 1).toString().padStart(2, "0");
                const day = dateTime.getDate().toString().padStart(2, "0");
                const hours = dateTime.getHours().toString().padStart(2, "0");
                const minutes = dateTime.getMinutes().toString().padStart(2, "0");
                const seconds = dateTime.getSeconds().toString().padStart(2, "0");
                if (type == 'yyyy-MM-dd') {
                    return `${year}-${month}-${day}`
                } else if (type == 'yyyy-MM-dd HH:mm:ss') {
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
                } else if (type == 'yyyy-MM-dd HH:mm') {
                    return `${year}-${month}-${day} ${hours}:${minutes}`
                }
            },
            InsertDrawing(data, th) {
                return new Promise((resolve, reject) => {
                    insertFrameDownloadTuKuang('tk12').then(resTk => {
                        console.log("🚀 ~ insertFrameDownloadTuKuang ~ resTk:", resTk)
                        insertFrameDownloadTuQian(new URLSearchParams(new URL(window.location.href).search).get('id')).then(resTq => {
                            const arrList = [];
                            const stage = projectInfoStore.getProjectInfo('stage')
                            const countyOrganisationName = new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName')
                            const projectName = new URLSearchParams(new URL(window.location.href).search).get('projectName')
                            const projectCode = new URLSearchParams(new URL(window.location.href).search).get('projectCode')
                            // 使用 Promise.all 来等待所有的异步操作完成
                            Promise.all(data.map(async item => {
                                // const tzmc = item.drawingname
                                console.log(item);
                                
                                const res = await autoMatchingPreview(item.equipmentInfo.drawingid);
                                return {
                                    openUrl: res,
                                    openUrlTk: resTk,
                                    openUrlTq: resTq,
                                    sjdw: countyOrganisationName, // 单位
                                    gcmc: projectName, // 项目名称
                                    sjjd: stage === "1" ? "需求" : stage === "2" ? "可研" : stage === "3" ? "初设" : stage === "4" ? "施工" : stage === "5" ? "施工变更" : stage === "5" ? "竣工" : "", // 阶段
                                    time: this.min_formatDateTime(new Date(), 'yyyy-MM-dd'), // 时间
                                    th: item.th, // 图号
                                    tzmc: item.tm, // 图纸名称
                                    tzbl: '1:1000', // 比例
                                    drawingid: item.equipmentInfo.drawingid,
                                    sort:item.sort
                                };
                            })).then(results => {
                                try {
                                    arrList.push(...results); // 将结果添加到 arrList
                                    console.log(arrList, 'arrList');

                                    publicStore().oniframeMessage({
                                        type: "cadPreview",
                                        content: "Mx_zdTzfileTk",
                                        formData: {
                                            arrList: arrList,
                                        }
                                    });
                                    resolve()
                                } catch (error) {
                                    reject(error)
                                }

                            });
                        });
                    });
                })

            },
        },
        persist: true
    })
export default zdppDwgUpdate
