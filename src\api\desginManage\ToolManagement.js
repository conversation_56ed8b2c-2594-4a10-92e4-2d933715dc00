import request from '@/utils/request'

// 工具管理-绘制其他图元-图元类型
export function legendTypesList(data) {
    return request({
        url: '/base/legendType/legendTypes',
        method: 'post',
        data: data
    })
}

// 工具管理-绘制其他图元-根据图元类型获取对应的物料/模块列表
export function legendTypeModules(params) {
    return request({
        url: '/base/legendType/modules',
        method: 'post',
        params: params
    })
}

// 工具管理-绘制其他图元-查询对应的 legend_category 数组状态
export function getLegendCategories(data) {
    return request({
        url: '/base/legendType/getLegendCategories',
        method: 'post',
        data: data
    })
}

// 工具管理-插入图纸-获取左侧树的图纸类别数据
export function crtzReadTree(params) {
    return request({
        url: '/jsObjInsertingDraw/ReadTree',
        method: 'get',
        params: params
    })
}
// 工具管理-插入图纸-重新排列左侧树的图纸类别数据
export function crtzRealignTree(data) {
    return request({
        url: '/jsObjInsertingDraw/RealignTree',
        method: 'post',
        data: data
    })
}
// 工具管理-插入图纸-根据图纸类别 key 和名称获取图纸数据
export function crtzClickData(params) {
    return request({
        url: '/jsObjInsertingDraw/ClickData',
        method: 'post',
        data: params
    })
}
// 工具管理-插入图纸-根据图纸名称获取图纸数据
export function crtzGetDrawingData(params) {
    return request({
        url: '/jsObjInsertingDraw/GetDrawingData',
        method: 'get',
        params: params
    })
}

// 工具管理-自动匹配图纸-获取图纸树形数据
export function autoMatching(params) {
    return request({
        url: '/autoMatchingDrawing/autoMatching',
        method: 'get',
        params: params
    })
}

// 工具管理-自动匹配图纸-预览图纸数据
export function autoMatchingPreview(id) {
    return request({
        url: `/autoMatchingDrawing/preview/${id}`,
        method: 'get',
        responseType: 'blob'
    })
}

// 工具管理-自动匹配图纸-插入图纸
export function autoMatchingInsertDrawing(data) {
    return request({
        url: '/autoMatchingDrawing/insertDrawing',
        method: 'post',
        data: data,
    })
}

// 工具管理-插入图框-判断功能是否可用：【需求编制】设计阶段、【工程参数设置】为空禁用本功能
export function insertFrameCheckEnable(id) {
    return request({
        url: `/insertFrame/checkEnable/${id}`,
        method: 'get',
    })
}

// 工具管理-插入图框-获取表单数据
export function insertFrameinitialize(id) {
    return request({
        url: `/insertFrame/initialize/${id}`,
        method: 'get',
    })
}

// 工具管理-插入图框-图框数据
export function insertFrameDownloadTuKuang(id) {
    return request({
        url: `/insertFrame/downloadTuKuang/${id}`,
        method: 'get',
        responseType: 'blob'
    })
}

// 工具管理-插入图框-图签数据
export function insertFrameDownloadTuQian(id) {
    return request({
        url: `/insertFrame/downloadTuQian/${id}`,
        method: 'get',
        responseType: 'blob'
    })
}

// 工具管理-插入图框-保存
export function addEquipmentInfo (data) {
    return request({
        url: `/insertFrame/addEquipmentInfo`,
        method: 'post',
        data: data
    })
}

// 工具管理-插入图框-回显
export function insertFrameHuixian (id) {
    return request({
        url: `/insertFrame/huixian/${id}`,
        method: 'get',
    })
}

// 工具管理-图纸拆分-获取数据
export function drawingSplitting (params) {
    return request({
        url: `/drawingSplitting/queryLedgerByTaskId`,
        method: 'get',
        params: params
    })
}
// 工具管理-三率自查-回显
export function selectThreeSelfinspection (params) {
    return request({
        url: `/jsObjStandardSelfExam/getListForTaskid`,
        method: 'get',
        params: params
    })
}
// 工具管理-图纸目录-图纸数据获取
export function getTuZhiMuLuList (id) {
    return request({
        url: `/insertFrame/getTuZhiMuLuList/${id}`,
        method: 'get',
    })
}

// 工具管理-完整性-回显
export function Integrity (params) {
    return request({
        url: `/outcomeIntegrityAnalysis/checkResultGeneration`,
        method: 'get',
        params: params
    })
}

// 工具管理-图纸目录-图纸更新
export function updateTuZhi (data) {
    return request({
        url: `/insertFrame/updateTuZhi`,
        method: 'post',
        data: data
    })
}

// 工具管理-图纸目录-插入图纸并且保存图纸
export function addTuZhiMuLu (data) {
    return request({
        url: `/insertFrame/addTuZhiMuLu`,
        method: 'post',
        data: data,
        headers: {
            "Content-Type": "multipart/form-data",
        }
    })
}
// 插入图纸接口 图纸更新
export function addDrawingMuLu (data) {
    return request({
        url: `/insertFrame/addDrawingMuLu`,
        method: 'post',
        data: data,
    })
}
// 工具管理-批量打印-图幅数据获取
export function getTuFu (id) {
    return request({
        url: `/insertFrame/getTuFu/${id}`,
        method: 'get'
    })
}

// 工具管理-批量打印-图纸类型数据获取
export function getTuZhiLeiXing (id) {
    return request({
        url: `/insertFrame/getTuZhiLeiXing/${id}`,
        method: 'get'
    })
}

// 工具管理-批量打印-图纸列表
export function selectTzList (data) {
    return request({
        url: `/insertFrame/selectTzList`,
        method: 'post',
        data: data
    })
}

// 工具管理-线夹匹配-图纸列表
export function materialswireclipSelectList (params) {
    return request({
        url: `/system/materialswireclip/selectList`,
        method: 'get',
        params: params
    })
}

// 工具管理-线夹匹配-耐张数据
export function GetDataNaiZhang (params) {
    return request({
        url: `/system/materialswireclip/GetDataNaiZhang`,
        method: 'get',
        params: params
    })
}
export function AddDataBingGou (params) {
    return request({
        url: `/system/materialswireclip/add`,
        method: 'post',
        data: params
    })
}
// 工具管理-线夹匹配-并沟数据
export function GetDataBingGou (params) {
    return request({
        url: `/system/materialswireclip/GetDataBingGou`,
        method: 'get',
        params: params
    })
}

// 工具管理-线夹匹配-并沟数据
export function getWireClipMaterialsProject (data) {
    return request({
        url: `/system/materialswireclip/getWireClipMaterialsProject`,
        method: 'post',
        data: data
    })
}

// 工具管理-格式刷-校验模板id
export function ifUseFormatPainter (id,takId) {
    return request({
        url: `/insertFrame/gss/ifUse/${id}/${takId}`,
        method: 'get',
    })
}

// 工具管理-批量打印-合并pdf
export function hebingPdf (data) {
    return request({
        url: `/insertFrame/hebingPdf`,
        method: 'post',
        data,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        responseType: 'blob'
    })
}
// 工具管理-格式刷-校验格式刷图元是否可以进行格式刷
export function refreshLegendTypeKeyFormatPainter (data) {
    return request({
        url: `/insertFrame/gss/refreshLegendTypeKey`,
        method: 'post',
        data
    })
}

// 工具管理-格式刷-保存接口
export function gssRefreshFormatPainter (data) {
    return request({
        url: `/insertFrame/gss/refresh`,
        method: 'post',
        data
    })
}


// 工具管理-合理性分析
export function getreasonable (data) {
    return request({
        url: `/equipment-validation/validate`,
        method: 'get',
        params: data
    })
}









