import Cookies from 'js-cookie'

const useAppStore = defineStore(
    'app',
    {
        state: () => ({
            sidebar: {
                opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
                withoutAnimation: false,
                hide: false
            },
            device: 'desktop',
            size: Cookies.get('size') || 'default',
            mapIndex: 0,
            nameFlag: '',
            dataFlag: false,
            paramsList: {},
            currentMxAppMenu: {},
            componentKey: 0,
            projectInfo: {},
            property:{},
            iframe:'',
            iframeHide: '',
            intervalList:[],
            sdkClassName:'',
            intervalName:'',
            intervalLowList:[],
            intervalLowName:'',
            byqList:[],
            heightFile:[],
            lowFile: [],
            cableList:[]
        }),
        actions: {
            toggleSideBar(withoutAnimation, flag) {
                if (this.sidebar.hide) {
                    return false;
                }
                if (flag) {
                    this.sidebar.opened = false;
                } else {
                    this.sidebar.opened = !this.sidebar.opened
                }
                this.sidebar.withoutAnimation = withoutAnimation
                if (this.sidebar.opened) {
                    Cookies.set('sidebarStatus', 1)
                } else {
                    Cookies.set('sidebarStatus', 0)
                }
            },
            closeSideBar({withoutAnimation}) {
                Cookies.set('sidebarStatus', 0)
                this.sidebar.opened = false
                this.sidebar.withoutAnimation = withoutAnimation
            },
            toggleDevice(device) {
                this.device = device
            },
            setSize(size) {
                this.size = size;
                Cookies.set('size', size)
            },
            toggleSideBarHide(status) {
                this.sidebar.hide = status
            },
            /* clickMap(index) {
                 this.mapIndex = index
             },*/
            clickMap(e) {
                this.mapIndex = e.name
                this.currentMxAppMenu = e
                this.componentKey ++
            },
            closeMapDialog() {
                this.mapIndex = ''
                this.currentMxAppMenu = {}
            },
            clickName(flag) {
                this.nameFlag = flag
            },
            clickData(flag) {
                this.dataFlag = flag
            },
            addParams(params) {
                this.paramsList = params
            },
            addProjectInfo(params) {
                this.projectInfo = params
            },
            getProjectInfo(key) {
                return this.projectInfo[key];
            },
            //存储sdk属性数据
            setPropertyInfo(params){
                console.log("🚀 ~ setPropertyInfo ~ params:", params)
                
                this.property=params
            },
            //储存iframe
            setIframeInfo(params){
                console.log(params,'store')
                this.iframe=params
            },
            setIframeHideInfo(params){
                console.log(params,'store')
                this.iframeHide=params
            },
            
            //储存间隔高压数据
            setIntervalList(params,name){
                this.intervalList=params
                this.intervalName=name
            },
            //储存间隔低压数据
            setIntervalLowList(params,name){
                this.intervalLowList=params
                this.intervalLowName=name
            },
             //储存间隔高压图纸文件
             setHeightFile(heightFile){
                this.heightFile=heightFile
            },
            //储存间隔低压图纸文件
            setLowFile(lowFile){
                this.lowFile=lowFile
            },
             //储存变压器数据
            setByqList(params){
                this.byqList=params
            },
            //储存sdkclassName
            setSdkClassName(params){
                this.sdkClassName=params
            },
            //储存电缆数据
            setCable(params){
                this.cableList=params
            },
        }
    })

export default useAppStore
