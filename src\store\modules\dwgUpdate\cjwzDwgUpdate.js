import {
    saveRemoveUsingMaterials,
    querycalculate,
    generatorCjwzmxReport
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
const dwgUpdate = defineStore(
    'dwgUpdate',
    {
        state: () => ({
            cjwzTableData: undefined,
        }),
        actions: {
            getCjwzTableData(taskId) {
                return new Promise((resolve, reject) => {
                    let params = { projectId: taskId, state: 'Remove' }
                    let arrTemp = []
                    querycalculate(params).then(res => {
                        res.data.forEach(item => {
                            arrTemp.push({
                                removeusingId: item.removeusingId,
                                materialcodeerp: item.materialCodeERP,
                                materialname: item.materialName,
                                technicalprotocol: item.technicalProtocol,
                                materialdescription: item.spec,
                                designunit: item.designUnit,
                                num: item.num,
                                materialsprojectid: item.materialsProjectID,
                                fromSource: item.fromSource,
                                addTag: 0,
                                state: item.state,
                                remarks: item.remarks,
                                materialTypeName: item.materialTypeName
                            })
                        })
                        this.cjwzTableData = arrTemp
                        resolve()
                    })
                })
            },
            excelParamsFilter() {
                return new Promise((resolve, reject) => {
                    try {
                        const filterParams = this.cjwzTableData.filter(item => {
                            return item.fromSource == "拆旧物资表"
                        }).map(item => {
                            return {
                                id: item.removeusingId ? item.removeusingId : null,//有这个id是原有的，没有的是新增的
                                materialsProjectID: item.materialsprojectid,
                                prjTaskInfoId: publicStore().taskId,
                                materialCodeErp: item.materialcodeerp,
                                designMaterialProjectId: item.materialsprojectid,
                                materialName: item.materialname,
                                materialSpec: item.spec ? item.spec : item.materialdescription,
                                technicalProtocol: item.technicalprotocol,
                                designUnit: item.designunit ? item.designunit : item.designunit,
                                quantity: item.num,
                                addTag: item.addTag ? item.addTag : 0,
                                materialState: item.state
                            }
                        })
                        resolve(filterParams)
                    } catch (error) {
                        reject(error)
                    }
                })

            },
            // 更新cjwz表格数据
            async unDateCjwzExcel(taskId) {
                return new Promise(async (resolve, reject) => {
                    const params = await this.excelParamsFilter()
                    console.log('折旧物资', params);
                    saveRemoveUsingMaterials(params).then(async (res) => {
                        console.log(res);
                        try {
                            if (res.code == 200) {
                                await this.getCjwzTableData(taskId)
                                resolve()
                            }
                        } catch (error) {

                        }
                    })
                })
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        const files = event.data.params.formData.files;
                        const tableData = JSON.parse(event.data.params.formData.tableData)

                        const tableDataNew = tableData.map((item, index) => {
                            console.log(item, '拆旧项目lllllllll');

                            return {
                                xh: index + 1,
                                wlmc: item.materialname,
                                wlms: item.materialdescription,
                                dw: item.designunit,
                                sl: item.num,
                                bz: item.remarks,
                                wlId: item.materialsprojectid,
                            }
                        })
                        const fileFormData = new FormData()

                        if (files.length == 0) {
                            fileFormData.append('multipartFile', '')
                            console.log(files, 'filesfilesfilesfiles')
                        } else {
                            files.forEach(item => {
                                fileFormData.append('multipartFile', item)
                            })
                        }
                        fileFormData.append("cjwzmxList", JSON.stringify(tableDataNew))
                        fileFormData.append("stage", new URLSearchParams(new URL(window.location.href).search).get('stage'))
                        fileFormData.append("prjTaskInfoId", new URLSearchParams(new URL(window.location.href).search).get('id'))
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${new URLSearchParams(new URL(window.location.href).search).get('proCode')}-1` }))
                        generatorCjwzmxReport(fileFormData).then(res1 => {
                            console.log(res1, 'dddddddd');
                            if (res1.code == 200) resolve()

                            // callChildCMethod();
                        })
                    } catch (error) {
                        reject(error)
                    }
                })

            }
        },
        persist: true
    })
export default dwgUpdate
