import {
    getBasicForm,
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
import { getTuQianFileByPath } from "@/api/onlineDesign/index.js";
const jcylDwgUpdate = defineStore(
    'jcylDwgUpdate',
    {
        state: () => ({
            cjwzTableData: undefined,
        }),
        actions: {
            async jcylDwgUpdateFun(th) {
                return new Promise(async (resolve, reject) => {
                    try {
                        const res = await getBasicForm({ taskId: publicStore().taskId });
                        let fileBlobArr = [];
                        if (res.code == 200) {
                            for (let i = 0; i < res.data.length; i++) {
                                if (res.data[i].draw) {
                                    res.data[i].fileBlob = await getTuQianFileByPath({
                                        filePath: res.data[i].draw,
                                    });
                                }
                                fileBlobArr.push({
                                    fileBlob: res.data[i].fileBlob,
                                    drawPath: res.data[i].draw,
                                });
                            }
                            resolve({ data: res.data, fileBlobArr: fileBlobArr, th: th })
                        }
                    } catch (error) {
                        reject(error)
                    }

                })
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        //保存报表文件到成果目录
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('files', item)// multipartFile
                        })
                        fileFormData.append("name", event.data.params.content === "GXYL" ? "杆塔型式一览图" : "基础型式一览图");
                        fileFormData.append('taskId', event.data.params.formData.proId)
                        fileFormData.append('stage', event.data.params.formData.stage)
                        saveDwgInfo(fileFormData).then(res => {
                            if (res.coee == 200) resolve()
                            // if (res.code === 200) {
                            //     ElMessage.success("保存成功");
                            //     loading.close()
                            //     if (callChildC) {
                            //         callChildC();
                            //     }
                            // } else {
                            //     ElMessage.error(res.msg);
                            //     loading.close()
                            // }
                        })
                    } catch (error) {
                        reject(error)
                    }
                })

            }
        },
        persist: true
    })
export default jcylDwgUpdate
