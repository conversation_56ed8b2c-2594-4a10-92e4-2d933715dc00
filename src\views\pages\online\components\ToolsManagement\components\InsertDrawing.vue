<template>
  <!-- 插入图纸 -->
  <data-dialog @close="closeDialog" dataWidth="1100" v-if="appStore.mapIndex == '插入图纸'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        插入图纸
      </h4>
    </template>
    <template #body>
      <div class="photo-box">
        <div class="photo-left">
          <div class="photo-border">
            <span style="height: 30px; line-height: 30px; font-size: 14px;margin-left: 10px">图纸类别</span>
          </div>
          <div class="left-tree">
            <el-tree :data="treeData" default-expand-all node-key="drawingTypeKey" :current-node-key="firstNode"
              :props="{ label: 'drawingTypeName', children: 'children' }" @node-click="handleNodeClick">
              <template #default="{ node, data }">
                <span class="custom-node">
                  <img :src="data.children && data.children.length > 0 ? folderIcon : fileIcon" alt="icon"
                    class="custom-icon" />
                  <span ref="label" class="custom-label" :style="{
                    width: '140px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }">
                    <!-- 判断是否需要使用el-tooltip -->
                    <el-tooltip v-if="node.level > 3" class="box-item" effect="dark" :content="node.label"
                      placement="top">
                      {{ node.label }}
                    </el-tooltip>

                    <!-- 如果文本未溢出，直接展示文本 -->
                    <span v-else>{{ node.label }}</span>
                  </span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="photo-rightCmd">
          <div class="photo-border">
            <el-input v-model="input" size="small" style="width: 300px;height: 29px" placeholder="请输入图纸名称进行搜索"
              class="input-with-select">
              <template #append>
                <el-button icon="Search" @click="Search()" />
              </template>
            </el-input>
            <el-button style="margin-left: 20px" size="small" type="primary" round
              @click="InsertDrawing">插入到图纸</el-button>
          </div>
          <div style="display: flex; border: 2px solid #badddd">
            <div class="photo-table">
              <el-table border style="margin-top: -2px; height: 400px; width: 100%" :data="photoData"
                @selection-change="handleSelectionChange">
                <el-table-column align="center" type="index" label="序号" width="55"></el-table-column>
                <el-table-column width="55" align="center" type="selection"></el-table-column>
                <el-table-column align="center" prop="drawingname" label="图纸名称">
                  <template #default="scope">
                    <el-link @click="preview(scope.row)">{{ scope.row.drawingname }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="photo-imgCmd">
              <div style="display: flex; align-items: center; height: 40px">
                <div style="
                      width: 3px;
                      height: 20px;
                      background: rgb(7, 136, 138);
                      margin: 0px 10px;
                    "></div>
                <div style="color: #008486; font-weight: bold">预览</div>
              </div>
              <div style="height: 360px; overflow: hidden;">
                <div id="myiframeYl1" style="height: 360px;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import fileIcon from '@/assets/images/file5.png'; // 引入图片路径
import folderIcon from '@/assets/images/file8.png'; // 引入图片路径
import {
  addTuZhiMuLu,
  autoMatchingInsertDrawing,
  autoMatchingPreview,
  crtzClickData,
  crtzGetDrawingData,
  crtzReadTree,
  crtzRealignTree, insertFrameDownloadTuKuang, insertFrameDownloadTuQian,addDrawingMuLu
} from "@/api/desginManage/ToolManagement.js";
import { tansParams } from "@/utils/ruoyi.js";
import { getToken } from "@/utils/auth.js";
import { useRoute } from "vue-router";
import { inject } from 'vue';
import useProjectInfoStore from "@/store/modules/projectInfo.js";
const callChildC = inject('callChildC');
const urlRoute = new useRoute()
const route = useRoute();
let { proxy } = getCurrentInstance();
const appStore = useAppStore();
const closeDialog = () => {
  appStore.mapIndex = "";
  const myiframe = document.getElementById("myiframeYl1");
  const existingIframe = document.getElementById("MXCADYl1");
  if (existingIframe) {
    myiframe.removeChild(existingIframe);
  }
};
const firstNode = ref(null)
const iframe = ref(null);
const input = ref('')
const treeData = ref([])
const photoData = ref([])
const multipleSelection = ref([])
const projectInfoStore = useProjectInfoStore();
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}
// async function validateBlob(blob) {
//   try {
//     await blob.arrayBuffer();
//     return true;
//   } catch (error) {
//     return false;
//   }
// }
const InsertDrawing = () => {
  console.log('multipleSelection.value', multipleSelection.value);

  if (multipleSelection.value.length == 0) {
    proxy.$message.warning('至少勾选一条数据！');
    return;
  }
  insertFrameDownloadTuKuang('tk12').then(resTk => {
    console.log("🚀 ~ insertFrameDownloadTuKuang ~ resTk:", resTk)
    if (resTk.type == 'application/json') {
      proxy.$message.warning('图框不存在');
      return;
    }
    insertFrameDownloadTuQian(route.query.id).then(resTq => {
      if (resTq.type == 'application/json') {
        proxy.$message.warning('图签不存在');
        return;
      }
      const arrList = [];
      const stage = projectInfoStore.getProjectInfo('stage')
      const countyOrganisationName = projectInfoStore.getProjectInfo('countyOrganisationName')
      const projectName = projectInfoStore.getProjectInfo('projectName')
      const projectCode = projectInfoStore.getProjectInfo('projectCode')
      // 使用 Promise.all 来等待所有的异步操作完成
      Promise.all(multipleSelection.value.map(async item => {
        const tzmc = item.drawingname
        const res = await autoMatchingPreview(item.drawingid);
        return {
          openUrl: res,
          openUrlTk: resTk,
          openUrlTq: resTq,
          sjdw: countyOrganisationName, // 单位
          gcmc: projectName, // 项目名称
          sjjd: stage === "1" ? "需求" : stage === "2" ? "可研" : stage === "3" ? "初设" : stage === "4" ? "施工" : stage === "5" ? "施工变更" : stage === "5" ? "竣工" : "", // 阶段
          time: min_formatDateTime(new Date(), 'yyyy-MM-dd'), // 时间
          th: `${projectCode}-1`, // 图号
          tzmc: tzmc, // 图纸名称
          tzbl: '1:1000', // 比例
          drawingid: item.drawingid
        };
      })).then(results => {
        arrList.push(...results); // 将结果添加到 arrList
        console.log(arrList, 'arrList');
        oniframeMessage({
          type: "cadPreview",
          content: "Mx_TzfileTk",
          formData: {
            arrList: arrList
          }
        });
      });
    });
  });
}
// 将date转换为yyyy-MM-dd格式
const min_formatDateTime = (dateTime, type) => {
  const year = dateTime.getFullYear();
  const month = (dateTime.getMonth() + 1).toString().padStart(2, "0");
  const day = dateTime.getDate().toString().padStart(2, "0");
  const hours = dateTime.getHours().toString().padStart(2, "0");
  const minutes = dateTime.getMinutes().toString().padStart(2, "0");
  const seconds = dateTime.getSeconds().toString().padStart(2, "0");
  if (type == 'yyyy-MM-dd') {
    return `${year}-${month}-${day}`
  } else if (type == 'yyyy-MM-dd HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else if (type == 'yyyy-MM-dd HH:mm') {
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }
}
const handDrawingTypeKey = ref(null)
const handleNodeClick = (val, node) => {
  if (!node.hasOwnProperty('children')) {
    handDrawingTypeKey.value = val.drawingTypeKey
    crtzClickData({ drawingName: input.value, drawingTypeKey: val.drawingTypeKey }).then(res => {
      if (res.data.length === 0) {
        // Drawing("", jsObjInsertingDraw);
        // proxy.$message.warning("没有对应的图纸信息！");
        photoData.value = res.data
        // displayWindowSize();
      } else {
        photoData.value = res.data
      }
    })
  }
}
const dataList = () => {
  crtzReadTree().then(res => {
    treeData.value = convertcurrent(res.data)
    if (treeData.value[0].children[0].children[0].drawingTypeKey) {
      handDrawingTypeKey.value = treeData.value[0].children[0].children[0].drawingTypeKey;
      firstNode.value = handDrawingTypeKey.value;
      Search()
    }
  })
}
// 搜索
const Search = () => {
  //  if(input.value != ''){
  crtzClickData({ drawingName: input.value, drawingTypeKey: handDrawingTypeKey.value }).then(res => {
    if (res.data.length === 0) {
      // Drawing("", jsObjInsertingDraw);
      photoData.value = res.data
      // proxy.$message.warning("没有对应的图纸信息！");
      // displayWindowSize();
    } else {
      photoData.value = res.data
    }
  })
  //   crtzGetDrawingData({ drawingName: input.value }).then(res => {
  //   crtzRealignTree(res.data).then(res1 => {
  //     treeData.value = res1.data
  //     photoData.value = res.data
  //   })
  // })
  //  }
}
const preview = (row) => {
  autoMatchingPreview(row.drawingid).then(res => {
    oniframeMessage({
      type: "cadPreview",
      content: "Mx_cadPreview",
      formData: {
        openUrl: res,
      }
    })
  })
}
/*传过来的list 按照父子关系生成树结构*/
const exists = (rows, ParentKey) => {
  for (let i = 0; i < rows.length; i++) {
    if (rows[i].drawingTypeKey === ParentKey) {
      return true;
    }
  }
  return false;
};

const convertcurrent = (rows) => {
  let nodes = [];
  // 获取父级节点
  for (let i = 0; i < rows.length; i++) {
    let row = rows[i];
    if (!exists(rows, row.parentKey)) {
      nodes.push({
        drawingTypeKey: row.drawingTypeKey,
        drawingTypeName: row.drawingTypeName,
        state: row.state,
        attributes: row.drawingTypeKey
      });
    }
  }

  let toDo = [...nodes];  // 初始化待处理数组
  while (toDo.length) {
    let node = toDo.shift();  // 父节点
    // 获取子节点
    for (let i = 0; i < rows.length; i++) {
      let row = rows[i];
      if (row.parentKey === node.drawingTypeKey) {
        let child = {
          drawingTypeKey: row.drawingTypeKey,
          drawingTypeName: row.drawingTypeName
        };
        if (node.children) {
          node.children.push(child);
        } else {
          node.children = [child];
        }
        toDo.push(child);
      }
    }
  }
  return nodes;
};
const cadYlFun = () => {
  let params = tansParams({
    ...urlRoute.query,
    token: getToken() || urlRoute.query?.token,
    mod: 'preview'
  })
  const src = params.length !== 0
    ? `${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
    : `${import.meta.env.VITE_APP_MX_APP_SRC}`
  const iframeElement = document.createElement("iframe");
  iframe.value = iframeElement;
  nextTick(async () => {
    const myiframe = document.getElementById("myiframeYl1");
    iframeElement.src = src
    iframeElement.id = "MXCADYl1";
    iframeElement.style.width = "100%";
    iframeElement.style.height = "100%";
    myiframe.append(iframeElement);
    iframeElement.onload = () => {
      console.log(myiframe, 'myiframemyiframemyiframe')
      setTimeout(() => {
        oniframeMessage({
          type: "cadPreview",
          content: "Mx_cadPreview",
          formData: {
            openUrl: null,
          }
        })
      }, 2600)
    }

  });
}
// 处理点击事件
const oniframeMessage = (param) => {
  // const myiframe = document.querySelector("#myiframeYl1").querySelector("iframe");
  const myiframe = document.querySelector("#MXCADYl1")
  console.log(myiframe, '1111ddddddddd')
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = async(event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '插入图纸带有图框图签') {
      const files = event.data.params.formData.files
      const queryList = []
      for(const item of files){
        let content;
      await new Promise((resolve,reject)=>{
        const fileReader = new FileReader();
        const fileRes = fileReader.readAsDataURL(item.file)
        fileReader.onload = function(){
        content = fileReader.result
        console.log(content)
        if(content){
            resolve()
          }
        }
       })
        queryList.push({
          content:content,
          name: item.file.name,
          taskId: route.query.id,
          stage: route.query.stage,
          equipmentInfos: JSON.stringify({ drawingid: item.drawingid })
        })
      }
      console.log(queryList)
      addDrawingMuLu(queryList).then(res => {
        if (res.code === 200) {
          proxy.$message.success("操作成功");
        } else {
          proxy.$message.error(res.msg);
        }
        if (callChildC) {
          callChildC();
        }
      })
      // autoMatchingInsertDrawing({ taskid: route.query.id, stage: route.query.stage, drawingIdList: multipleSelection.value }).then(res => {
      //   if (res.code === 200) {
      //     proxy.$message.success("操作成功");
      //   } else {
      //     proxy.$message.error(res.msg);
      //   }
      //   if (callChildC) {
      //     callChildC();
      //   }
      // })
    }
  }
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    console.log(newInfo, oldInfo);
    if (newInfo == "插入图纸") {
      dataList()
      input.value = ''
      cadYlFun()
    }
  }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;

::v-deep .el-tree .is-current>.el-tree-node__content {
  background: rgba(80, 169, 170, 0.4);
}

::v-deep .el-tree .el-tree-node :hover {
  background: rgba(80, 169, 170, 0.1) !important;
}
</style>