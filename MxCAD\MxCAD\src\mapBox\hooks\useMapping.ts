import { onMounted, onUnmounted, ref } from "vue";
import SDKClickInfo from "../sdkClickInfo";
import SDKDrawState from "../sdkDrawState";
// 定义选项类型
interface HandlerOptions {
  [key: string]: any; // 动态键值对支持各种参数
  options?: Record<string, any>; // 通用选项字段
}

/** 定义消息类型
 * greeting  地图功能
 * cad  纯cad功能
 * sdkcad  sdk和cad通用功能
 * backgroundcad  使用cad功能  但处于无关sdk或者cad页面
 */
type MessageType = "greeting" | "onlycad" | "sdkcad" | "backgroundcad";

// 定义映射表的单项
interface CommandHandler {
  cmd: string; // 命令名称
  handler: (options: HandlerOptions) => void; // 处理函数
}

// 消息映射表类型
type MessageHandlers = Record<MessageType, CommandHandler[]>;

let messageEvent = null;

export function useMessageHandlers(map: any, event: any) {
  messageEvent = event;

  // 消息处理器映射
  const messageHandlers: MessageHandlers = {
    greeting: [
      {
        //工程复用
        cmd: "SDK_versionExtend",
        handler: (options) => versionExtend(map, options),
      },
      //#region 架空线路
      {
        cmd: "SDK_xlhz",
        handler: (options) => addConductorLine(map, options),
      },
      //双回
      {
        cmd: "SDK_slxlhz",
        handler: (options) => addShConductorLine(map, options),
      },
      {
        cmd: "SDK_gtlxxz",
        handler: (options) => insertPoleDevice(map, options),
      },
      {
        cmd: "SDK_selectEntity",
        handler: (options) => selectSDKEntity(map, options),
      },
      {
        cmd: "SDK_zsbd",
        handler: (options) => addTransformer(map, options),
      },
      {
        cmd: "SDK_loadMap",
        handler: (options) => console.log("加载地图线:", options),
      },
      {
        cmd: "SDK_zssbsblx",
        handler: (options) => insertOpDeviceFun(map, options),
      },
      {
        cmd: "SDK_zsbyqhz",
        handler: () => {
          window.parent.postMessage(
            { type: "SDK_reply", content: "SDK_zsbyqhz" },
            "*"
          );
        },
      },
      {
        cmd: "SDK_cgt",
        handler: (options) => batchInsertPoles(map, options),
      },
      {
        cmd: "SDK_lxhz",
        handler: (options) => addStayDevice(map, options),
      },
      {
        //接地绘制
        cmd: "SDK_jdhz",
        handler: (options) => addJdhz(map, options),
      },
      {
        cmd: "SDK_dxhz",
        handler: (options) => extendConductorLine(map, options),
      },
      //#endregion
      //#region 电缆线路
      {
        cmd: "SDK_add_cable_line",
        handler: (options) => addCableLine(map, options),
      },
      //基于土建路径
      {
        cmd: "SDK_add_soil_line",
        handler: (options) => addSoilLine(map, options),
      },
      //管道增线
      {
        cmd: "SDK_add_piping",
        handler: (options) => addPiping(map, options),
      },
      {
        cmd: "SDK_extend_cable_line",
        handler: (options) => extendCableLine(map, options),
      },
      //插入电缆头-绘制中间接头
      {
        cmd: "SDK_extend_cable_dlt",
        handler: (options) => extendCableDlt(map, options),
      },
      //插入电缆头- 等分插入接头
      {
        cmd: "SDK_equally_connector",
        handler: (options) => equallyConnector(map, options),
      },
      //插入电缆头-等距插入接头
      {
        cmd: "SDK_equidistant_connector",
        handler: (options) => equidistantConnector(map, options),
      },
      {
        //绘制电缆分支箱
        cmd: "SDK_dlfzx",
        handler: (options) => addCableBox(map, options),
      },
      {
        //绘制电缆井
        cmd: "SDK_well",
        handler: (options) => insertWell(map, options),
      },
      //#endregion
      //#region 配电站房
      {
        cmd: "SDK_zfty",
        handler: (options) => addStation(map, options),
      },
      //#endregion
      {
        cmd: "SDK_rename_life",
        handler: (options) => renameLife(map, options),
      },
      //接地绘制
      {
        cmd: "SDK_jdhz",
        handler: (options) => addJdhz(map, options),
      },
      //绘制电缆通道
      {
        cmd: "SDK_dltd",
        handler: (options) => addDltd(map, options),
      },
      //间隔拼接
      {
        cmd: "SDK_add_interval_splicing",
        handler: (options) => addInterval(map, options),
      },
      //获取全量间隔
      {
        cmd: "SDK_add_interval_full",
        handler: (options) => fullInterval(map, options),
      },
      {
        cmd: "SDK_openProfile",
        handler: (options) => openProFile(map, options),
      },
      //剖面保存数据
      {
        cmd: "SDK_proFile",
        handler: (options) => addProFile(map, options),
      },
      //替换图元
      {
        cmd: "SDK_replace_elements",
        handler: (options) => replaceElements(map, options),
      }, //获取通道内电缆
      {
        cmd: "SDK_tongdao",
        handler: (options) => sdkTongDao(map, options),
      },
      //获取电缆信息
      {
        cmd: "SDK_taodao_details",
        handler: (options) => sdkTongDaoDetails(map, options),
      },

      //选中设备
      {
        cmd: "SDK_click_device",
        handler: (options) => sdkClickDevice(map, options),
      },
      //变压器增容
      {
        cmd: "SDK_byqzr",
        handler: (options) => sdkByqZr(map, options),
      },
      //变压器增容 ---通用保存台账

      {
        cmd: "SDK_zrsave",
        handler: (options) => sdkZrSave(map, options),
      },

      //#endregion
    ],
    sdkcad: [
      //地图和cad有同样的功能命令
      {
        //撤销
        cmd: "undo",
        handler: (options) => undo(map, options),
      },
      {
        //重做
        cmd: "redo",
        handler: (options) => redo(map, options),
      },
      {
        //删除
        cmd: "removeDevice",
        handler: (options) => removeDevice(map, options),
      },
    ],
    onlycad: [],
    backgroundcad: [],
  };

  // 根据消息类型和命令匹配处理器
  const findHandler = (
    type: MessageType,
    cmd: string
  ): ((options: HandlerOptions) => void) | undefined => {
    const handlers = messageHandlers[type];
    return handlers?.find((handler) => handler.cmd === cmd)?.handler;
  };

  // 消息处理函数
  const handleMessage = (
    type: MessageType,
    cmd: string,
    options: HandlerOptions = {}
  ): void => {
    const handler = findHandler(type, cmd);
    if (handler) {
      console.log("handleMessage-type:", type, "handleMessage-cmd:", cmd);
      handler(options);
    } else {
      console.warn(`未处理的命令: ${cmd}`);
    }
  };

  // 监听消息事件
  // const onMessageReceived = (event: MessageEvent): void => {
  //     const {type, content: cmd, ...options} = event.data;
  //     handleMessage(type as MessageType, cmd, options);
  // };

  // onMounted(() => {
  //     window.addEventListener('message', onMessageReceived);
  // });

  // onUnmounted(() => {
  //     window.removeEventListener('message', onMessageReceived);
  // });

  // 返回处理函数供其他地方调用（如果需要）
  return { handleMessage };
}

let mapSDKType = "";

// 子页面发送到父页面
const sendMessage = (params) => {
  console.log("发送到父页面", messageEvent.source);
  messageEvent.source.postMessage(
    { messageId: messageEvent.data.id, params },
    "*"
  );
};

// 新建架空线路(未修改杆塔类型调用)
function addConductorLine(map: any, options: object) {
  mapSDKType = "新建架空线路";
  console.log("新建架空线路-addConductorLine：", options);
  SDKDrawState.setCurrentDrawInfo("SDK_xlhz", "addConductorLine", true);
  map.addConductorLine(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addConductorLine", false);
    console.log("新建架空线路-addConductorLine-回调", params);
    //解析导线的 前后关联对象ID在 SDK返回数据里查询不到的 用详细方法获取得到物理杆ID
    //需调用获取详细信息的方法 得到起始选择的物理杆
    //处理杆号名称
    let poleSiteArr = [];
    const poleSite = params.result.data.filter(
      (res) => res.CLASS_NAME === "PWPolesitePSR"
    );
    poleSite.forEach((item, index) => {
      if (index === 0 || index === poleSite.length - 1) {
        poleSiteArr.push(item.ID);
      }
    });
    if (params.status === "000000") {
      dealSDKDrawLineData(
        params,
        map,
        { type: "reply", params, mapSDKType, cmd: "SDK_xlhz" },
        { poleSiteArr, options }
      );
      // dealSDKDrawTowerLineData(params, map, {type: 'reply', params, mapSDKType, cmd: "SDK_xlhz"})
    } else {
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_xlhz" });
    }
  });
}
function rearrangePoleNumber(map, poleIds, obj) {
  const options1 = {
    poleIds,
    prefix: obj.ganPrefix,
    suffix: obj.ganSuffix,
    sortMode: parseInt(obj.ganAddNum),
    startIndex: parseInt(obj.ganStart),
  };
  map.rearrangePoleNumber(options1, (params) => {
    //   console.log('杆号重排响应结果：', params);
    if (params.status === "error") {
      sendMessage({ type: "showMessage", params: { content: params.message } });
    }
  });
}
// 新建双回架空线路(未修改杆塔类型调用)
function addShConductorLine(map: any, options: object) {
  mapSDKType = "新建架空线路";
  SDKDrawState.setCurrentDrawInfo("SDK_slxlhz", "addShConductorLine", true);
  map.extendSamePoleLine(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addShConductorLine", false);
    //解析导线的 前后关联对象ID在 SDK返回数据里查询不到的 用详细方法获取得到物理杆ID
    //需调用获取详细信息的方法 得到起始选择的物理杆
    if (params.status === "000000") {
      dealSDKDrawLineData(
        params,
        map,
        { type: "reply", params, mapSDKType, cmd: "SDK_slxlhz" },
        ""
      );
      // dealSDKDrawTowerLineData(params, map, {type: 'reply', params, mapSDKType, cmd: "SDK_xlhz"})
    } else {
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_slxlhz" });
    }
  });
}
// 新建架空线路(修改杆塔类型后调用)
function insertPoleDevice(map: any, options: object) {
  mapSDKType = "插入杆塔设备";
  console.log("修改杆塔类型后调用-插入杆塔设备：", options);
  SDKDrawState.setCurrentDrawInfo("SDK_gtlxxz", "addPoleDevice", true);
  map.addPoleDevice(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addPoleDevice", false);
    console.log("修改杆塔类型后调用-插入杆塔设备-回调", params);
    //解析导线的 前后关联对象ID在 SDK返回数据里查询不到的 用详细方法获取得到物理杆ID
    //需调用获取详细信息的方法 得到起始选择的物理杆
    if (params.status === "000000") {
      dealSDKDrawLineData(
        params,
        map,
        { type: "reply", params, mapSDKType, cmd: "SDK_gtlxxz" },
        ""
      );
      // dealSDKDrawTowerLineData(params, map, {type: 'reply', params, mapSDKType, cmd: "SDK_gtlxxz"})
    } else {
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_gtlxxz" });
    }
  });
}

//接地绘制
function addJdhz(map: any, options: object) {
  mapSDKType = "接地绘制";
  map.addStayDevice(options, (params) => {
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_jdhz" });
  });
}

//间隔拼接
async function addInterval1(map: any, options: Array<any>): Promise<void> {
  if (!map?.createBay) {
    throw new Error("无效的地图实例：缺少 createBay 方法");
  }

  mapSDKType = "间隔拼接";
  let hasError = false;
  for (let index = 0; index < options.length; index++) {
    const element = options[index];

    try {
      // 将回调函数转换为Promise
      await new Promise<void>((resolve, reject) => {
        map.createBay(element, (params: any) => {
          try {
            if (params.status === "error") {
              const errorMessage = params.result;
              reject(new Error(errorMessage));
            } else {
              resolve(); // 操作成功完成
            }
          } catch (sendError) {
            reject(new Error(`消息发送失败: ${sendError.message}`));
          }
        });
        // 可选：添加超时拒绝
        setTimeout(() => {
          reject(new Error(`创建间隔操作超时`));
        }, 5000); // 5秒超时
      });
    } catch (error) {
      console.error(`第 ${index + 1} 个间隔处理失败:`, error);
      sendMessage({ type: "showMessage", params: { content: error.message } });
      hasError = true;
      return;
      // 可选：继续执行后续操作或终止
      // throw error; // 如果希望失败后立即停止
    }
  }
  return !hasError;
}
async function addInterval(map, options) {
  try {
    const isSuccess = await addInterval1(map, options);
    console.log("所有间隔添加完成");
    if (isSuccess) {
      map.fixGraph((params) => {
        console.log(params, "一键调图");
        sendMessage({
          type: "reply",
          params,
          mapSDKType,
          cmd: "SDK_add_interval_splicing",
        });
      });
    }
  } catch (error) {
    console.error("主流程捕获错误:", error);
  }
}
//全量间隔
async function fullInterval(map, options) {
  mapSDKType = "获取全量间隔";
  SDKDrawState.setCurrentDrawInfo(
    "SDK_add_interval_full",
    "fullInterval",
    true
  );
  map.getStationAllBayInfo(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("fullInterval", false);
    console.log("获取全量间隔响应结果：", params);
    sendMessage({
      type: "",
      params,
      mapSDKType,
      cmd: "SDK_add_interval_full",
    });
  });
}
//打开剖面
async function openProFile(map, options) {
  map.profileMaintenance((params) => {
    console.log("剖面编辑数据保存响应结果", params);
  });
}
//剖面保存
async function addProFile(map, options) {
  let mapSDKType = "剖面编辑数据保存";
  map.saveProfileDatas((params) => {
    console.log("剖面编辑数据保存响应结果", params);
    if (params.status === "000000") {
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_proFile" });
      if (params.message === "剖面编辑数据保存完成") {
        map.quitProfileMaintenance((params) => {
          console.log("退出剖面维护响应结果", params);
        });
      }
    }
  });
}
//替换图元

async function replaceElements(map, options) {
  console.log(options, "111");
  let mapSDKType = "替换图元";
  SDKDrawState.setCurrentDrawInfo(
    "SDK_replace_elements",
    "setPoleDevice",
    true
  );
  map.setPoleDevice(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("setPoleDevice", false);
    console.log(params, "paramsparamsparams");
    if (params.status === "000000") {
      sendMessage({
        type: "reply",
        params,
        mapSDKType,
        cmd: "SDK_replace_elements",
      });
    }
  });
}

//获取通道内电缆

async function sdkTongDao(map, options) {
  let mapSDKType = "显示通道内电缆";
  SDKDrawState.setCurrentDrawInfo("SDK_tongdao", "getCableInChannel", true);
  map.getCableInChannel((params) => {
    console.log("显示通道内电缆响应结果", params);
    if (params.status === "000000") {
      SDKDrawState.changeCurrentDrawInfo("getCableInChannel", false);
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_tongdao" });
    }
  });
}

async function sdkTongDaoDetails(map, options) {
  const mapSDKType = "获取电缆信息";
  SDKDrawState.setCurrentDrawInfo("SDK_taodao_details", "getDeviceInfo", true);

  try {
    // 1. 获取初始设备信息
    const initialData = await getDeviceInfoAsync(map, options);
    const list = initialData.result.map((item) => ({
      name1: item.NAME,
      id: item.DEV_ID,
      name2: "", // 初始化 name2
    }));

    // 2. 存储每一层级的查询结果，用于验证层级关系
    const hierarchy = {
      level1: initialData.result,
      level2: null,
      level3: null,
    };

    // 3. 递归获取父级设备信息（最多3层）
    let currentOptions = initialData.result.map((item) => ({
      className: item.PARENT_CLASS_NAME,
      devId: item.PARENT_ID,
    }));

    // 第二次查询（level2）
    if (currentOptions.length) {
      hierarchy.level2 = (await getDeviceInfoAsync(map, currentOptions)).result;
      currentOptions = hierarchy.level2.map((item) => ({
        className: item.PARENT_CLASS_NAME,
        devId: item.PARENT_ID,
      }));
    }

    // 第三次查询（level3）
    if (currentOptions.length) {
      hierarchy.level3 = (await getDeviceInfoAsync(map, currentOptions)).result;
    }

    // 4. 验证层级关系并赋值 name2
    list.forEach((item, index) => {
      const level1Item = hierarchy.level1[index];
      if (!level1Item) return;

      // 检查层级关系：
      // level1.PARENT_ID === level2.DEV_ID
      // level2.PARENT_ID === level3.DEV_ID
      const level2Match = hierarchy.level2?.find(
        (l2) => l2.DEV_ID === level1Item.PARENT_ID
      );
      if (!level2Match) return;

      const level3Match = hierarchy.level3?.find(
        (l3) => l3.DEV_ID === level2Match.PARENT_ID
      );
      if (level3Match) {
        item.name2 = level3Match.NAME; // 只有满足条件时才赋值
      }
    });

    console.log("最终数据:", list);
    if (list.length > 0) {
      SDKDrawState.changeCurrentDrawInfo("getDeviceInfo", false);
      sendMessage({
        type: "reply",
        list,
        mapSDKType,
        cmd: "SDK_taodao_details",
      });
    }
  } catch (error) {
    console.error("获取设备信息失败:", error);
    SDKDrawState.changeCurrentDrawInfo("getDeviceInfo", false);
  }
}

// 封装 getDeviceInfo 为 Promise 形式
function getDeviceInfoAsync(map, options) {
  return new Promise((resolve, reject) => {
    map.getDeviceInfo(options, (params) => {
      if (params.status === "000000") {
        resolve(params);
      } else {
        reject(new Error(`获取设备信息失败: ${params.status}`));
      }
    });
  });
}

//选中设备

async function sdkClickDevice(map, options) {
  // let mapSDKType='选中设备'
  SDKDrawState.setCurrentDrawInfo("SDK_click_device", "selectDevice", true);
  map.selectDevice(options, (params) => {
    console.log("选中设备响应结果：", params);
    if (params.status === "000000") {
      SDKDrawState.changeCurrentDrawInfo("selectDevice", false);
      // sendMessage({type: 'reply', params, mapSDKType, cmd: "SDK_click_device"})
    }
  });
}

//变压器增容
async function sdkByqZr(map, options) {
  let mapSDKType = "变压器增容";
  SDKDrawState.setCurrentDrawInfo("SDK_byqzr", "getSubDeviceInfo", true);
  map.getSubDeviceInfo(options, (params) => {
    console.log("获取子设备信息数据响应结果：", params);
    if (params.status === "000000") {
      SDKDrawState.changeCurrentDrawInfo("getSubDeviceInfo", false);
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_byqzr" });
    }
  });
}

//通用保存

async function sdkZrSave(map, options) {
  let optios1 = [JSON.parse(options)];
  SDKDrawState.setCurrentDrawInfo("SDK_zrsave", "setDeviceInfo", true);
  map.setDeviceInfo(optios1, (params) => {
    console.log("保存设备信息数据响应结果：", params);
    if (params.status === "000000") {
      SDKDrawState.changeCurrentDrawInfo("setDeviceInfo", false);
    }
  });
}

//处理电缆通道获取坐标信息
async function getDeviceCoordinates(map, dataList) {
  const cables = dataList.filter((o) => o.CLASS_NAME === "CableChannelSec");
  const cables1 = dataList.filter((o) => o.CLASS_NAME === "Well");
  const headXYZ = [];
  // 提取获取设备信息的逻辑
  const getDeviceInfos = async (device, name) => {
    try {
      let options = [
        {
          devId: device,
          className: name,
        },
      ];

      const response = await new Promise((resolve, reject) => {
        map.getDeviceInfo(options, (response) => {
          let towerXY = {
            X: response.result[0].SHAPE.match(
              /(\d+\.\d+)\s+(\d+\.\d+)/g
            )[0].split(" ")[0],
            Y: response.result[0].SHAPE.match(
              /(\d+\.\d+)\s+(\d+\.\d+)/g
            )[0].split(" ")[1],
          };
          if (response.status === "000000") {
            resolve({
              X: towerXY.X,
              Y: towerXY.Y,
              DEV_ID: response.result[0].DEV_ID,
            });
          } else {
            reject("错误");
          }
        });
      });
      return response;
    } catch (error) {
      console.error("Error fetching device info:", error);
      return null;
    }
  };
  for (let i = 0; i < cables.length; i++) {
    let headObj;
    let tallObj;
    if (cables[i].HEAD_DEV_CLASS_NAME && cables[i].TAIL_DEV_CLASS_NAME) {
      headObj = findValue(cables1, cables[i].HEAD_DEV_ID, "");
      tallObj = findValue(cables1, "", cables[i].TAIL_DEV_ID);
    } else if (
      cables[i].HEAD_DEV_CLASS_NAME &&
      !cables[i].TAIL_DEV_CLASS_NAME
    ) {
      headObj = findValue(cables, cables[i].HEAD_DEV_ID, "");
    }
    if (headObj) {
      const a = await getDeviceInfos(
        cables[i].HEAD_DEV_ID,
        cables[i].HEAD_DEV_CLASS_NAME
      );
      headXYZ.push(a);
    }
    if (tallObj) {
      const b = await getDeviceInfos(
        cables[i].TAIL_DEV_ID,
        cables[i].TAIL_DEV_CLASS_NAME
      );
      headXYZ.push(b);
    }
  }
  return headXYZ; // 返回所有有效的坐标
}

//查找head或tall是否存在arr数组里 存在返回false 不存在返回true
function findValue(arr, head, tall) {
  let found;
  if (head) {
    found = arr.find((item) => item.DEV_ID === head);
  }
  if (tall) {
    found = arr.find((item) => item.DEV_ID === tall);
  }
  if (!found) return true;
  if (found) return false;
}

//绘制电缆通道
function addDltd(map: any, options: object) {
  mapSDKType = "绘制电缆通道";
  map.drawCableChannel(options, async (params) => {
    try {
      const dataList = params.result.data;
      const list = await getDeviceCoordinates(map, dataList);
      const setList = new Set();
      const unipment = [];
      list.forEach((item) => {
        if (!setList.has(item.DEV_ID)) {
          setList.add(item.DEV_ID);
          unipment.push(item);
        }
      });
      console.log(unipment, "去重后");
      sendMessage({
        type: "reply",
        params,
        mapSDKType,
        cmd: "SDK_dltd",
        unipment,
      });
    } catch (error) {
      console.log(error, "报错");
    }
  });
}

// 选择地图上的设备
function selectSDKEntity(map: any, options: any) {
  mapSDKType = "选择地图对象";
  SDKDrawState.setCurrentDrawInfo("SDK_selectEntity", "sdkSelectEntity", true);
  if (
    !SDKClickInfo.hasClickInfo() ||
    options.classNames.indexOf(SDKClickInfo.getClickInfoClassName()) < 0
  ) {
    sendMessage({ type: "showMessage", params: { content: options.errorMsg } });
    return;
  }
  SDKDrawState.changeCurrentDrawInfo("sdkSelectEntity", false);
  sendMessage({
    type: "reply",
    params: SDKClickInfo.getCurrentClickInfoExtend(),
    mapSDKType,
    cmd: "SDK_selectEntity",
  });
}

// 插入柱上变台：
function addTransformer(map: any, options: object) {
  mapSDKType = "插入柱上变台";
  SDKDrawState.setCurrentDrawInfo("SDK_zsbd", "addTransformer", true);
  map.addTransformer(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addTransformer", false);
    console.log("插入柱上变台addTransformer：", params);

    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_zsbd" });
  });
}

// 插入柱上设备
function insertOpDeviceFun(map: any, options: any) {
  mapSDKType = "柱上设备绘制";
  // 需要增加判断如果drawingmethod=block，使用华云画图功能， 如果是Point， 不需要华云画图,点击绘制按钮之后需要点击设备
  const { drawingmethod, symbolId } = options;
  console.log("rest", symbolId);

  if (drawingmethod === "Block") {
    SDKDrawState.setCurrentDrawInfo("SDK_zssbsblx", "addOpDevice", true);
    map.addOpDevice({ symbolId }, (params) => {
      SDKDrawState.changeCurrentDrawInfo("addOpDevice", false);

      console.log("插入柱上设备：", params);
      sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_zssbsblx" });
    });
  }
}

//插入拉线设备
function addStayDevice(map: any, options: any) {
  mapSDKType = "插入拉线设备";
  SDKDrawState.setCurrentDrawInfo("SDK_lxhz", "addStayDevice", true);
  map.addStayDevice(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addStayDevice", false);
    console.log("插入拉线设备：", params);
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_lxhz" });
  });
}

//预处理sdk返回的数据 主要是获取物理杆
function dealSDKDrawLineData(
  params: any,
  map: any,
  seedMsgParams: any,
  obj: any
) {
  const { data } = params.result;
  const lineDatas = data.filter(
    (o) =>
      (o.CLASS_NAME === "PWConductorSecPSR" ||
        o.CLASS_NAME === "PWCableSecPSR") &&
      o.addFlag
  );
  //导线前后关联的运行杆不在sdk返回的数据里 想办法得到运行杆再得到物理杆
  //电缆段和绝缘导线 需要处理拓扑字段不一样
  const findLineKeys = [
    ["HEAD_DEV_ID", "HEAD_DEV_CLASS_NAME"],
    ["TAIL_DEV_ID", "TAIL_DEV_CLASS_NAME"],
  ];
  const findCableKeys = [
    ["START_DEV_ID", "START_DEV_CLASS_NAME"],
    ["END_DEV_ID", "END_DEV_CLASS_NAME"],
  ];
  const findTowers = [];
  for (let itemLine of lineDatas) {
    const findKeys =
      itemLine.CLASS_NAME === "PWCableSecPSR" ? findCableKeys : findLineKeys;
    for (let key of findKeys) {
      //关联了运行杆 但是返回的数据里不存在 或者 关联了其他图元 但是没有坐标信息
      if (
        ((itemLine[key[1]] === "PWPolesitePSR" &&
          !data.some((o) => o.DEV_ID === itemLine[key[0]] && o.POLE_PSR_ID)) ||
          (itemLine[key[1]] !== "PWPolesitePSR" &&
            !data.some((o) => o.DEV_ID === itemLine[key[0]] && o.X && o.Y))) &&
        !findTowers.some((o) => o.devId === itemLine[key[0]])
      ) {
        findTowers.push({
          className: itemLine[key[1]],
          devId: itemLine[key[0]],
        });
      }
    }
  }
  console.log("缺少运行杆塔信息：", findTowers);
  map.getDeviceInfo(findTowers, (extend) => {
    if (extend.status === "000000") {
      //追加运行杆数据
      extend.result.forEach((element) => {
        const index = params.result.data.findIndex(
          (o) => o.DEV_ID === element.DEV_ID
        );
        if (index !== -1) {
          params.result.data.splice(index, 1);
        }
        params.result.data.unshift(element);
      });
      //得到物理杆
      const towerDatas = extend.result
        .filter((o) => o.CLASS_NAME === "PWPolesitePSR")
        .map((element) => {
          return {
            devId: element.POLE_PSR_ID,
            className: element.POLE_CLASS_NAME ?? "PWPolePSR",
          };
        });
      map.getDeviceInfo(towerDatas, (tower) => {
        if (tower.status === "000000") {
          //追加物理杆数据
          tower.result.forEach((element) => {
            const index = params.result.data.findIndex(
              (o) => o.DEV_ID === element.DEV_ID
            );
            if (index !== -1) {
              params.result.data.splice(index, 1);
            }
            params.result.data.unshift(element);
          });
          sendMessage(seedMsgParams);
          if (obj) {
            let objs = JSON.parse(obj.options.extendInfo.extendData);
            if (obj.poleSiteArr.length == 1) {
              const options = {
                id: obj.poleSiteArr[0],
                className: "PWPolesitePSR",
                rename: objs.ganPrefix + objs.ganStart + objs.ganSuffix,
                alisasName: objs.ganPrefix + objs.ganStart + objs.ganSuffix,
              };
              map.deviceRename(options, (params) => {
                console.log("设备重命名响应结果", params);
              });
            } else {
              rearrangePoleNumber(map, obj.poleSiteArr, objs);
            }
          }
        } else if (towerDatas.length > 0) {
          console.log("缺少物理杆塔数据：", tower, towerDatas);
          sendMessage({
            type: "showMessage",
            params: { content: "缺少物理杆塔数据!" },
          });
        } else {
          sendMessage(seedMsgParams);
        }
      });
    } else if (findTowers.length > 0) {
      console.log("缺少运行杆塔数据：", extend);
      sendMessage({
        type: "showMessage",
        params: { content: "缺少运行杆塔数据!" },
      });
    } else {
      sendMessage(seedMsgParams);
    }
  });
}

//延长架空线路-导线绘制
function extendConductorLine(map: any, options: any) {
  mapSDKType = "导线绘制";
  console.log(`${mapSDKType}:`, options);
  SDKDrawState.setCurrentDrawInfo("SDK_dxhz", "extendConductorLine", true);
  map.extendConductorLine(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("extendConductorLine", false);
    console.log(`${mapSDKType}-回调`, params);
    //解析导线的 前后关联对象ID在 SDK返回数据里查询不到的 用详细方法获取得到物理杆ID
    //需调用获取详细信息的方法 得到起始选择的物理杆
    if (params.status === "000000") {
      dealSDKDrawLineData(
        params,
        map,
        { type: "reply", params, mapSDKType, cmd: "SDK_dxhz" },
        ""
      );
    } else {
      sendMessage({ type: "showMessage", params: { content: params.message } });
    }
  });
}

//重命名
function renameLife(map: any, options: any) {
  mapSDKType = "重命名";
  map.deviceRename(options, (params) => {
    console.log("设备重命名响应结果", params);
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_rename_life" });
  });
}

// 插入杆塔
function batchInsertPoles(map: any, params: any) {
  //TODO 图元类型判断待封装
  if (
    !SDKClickInfo.hasClickInfo() ||
    SDKClickInfo.getClickInfoClassName() !== "PWConductorSecPSR"
  ) {
    sendMessage({
      type: "showMessage",
      params: { content: "请选择架空导线!" },
    });
    return;
  }
  const lineLength = SDKClickInfo.getCurrentClickInfo()?.SPAN;
  if (!lineLength || lineLength === 0) {
    sendMessage({
      type: "showMessage",
      params: { content: "获取不到架空导线长度!" },
    });
    return;
  }
  console.log("插入杆塔页面参数", params);
  mapSDKType = "插入杆塔";
  let poleNames = [];
  if (params.checkRadio === "1") {
    //自由插入
    let options = {
      symbolId: params.symbolId,
    };
    SDKDrawState.setCurrentDrawInfo("SDK_cgt", "addDevice", true);
    map.addDevice(options, (res) => {
      SDKDrawState.changeCurrentDrawInfo("addDevice", false);
      console.log("自由插入杆塔设备-addDevice-回调：", res);
      if (res.status === "000000") {
        sendMessage({ type: "reply", res, mapSDKType, cmd: "SDK_cgt" });
      } else {
        sendMessage({
          type: "showMessage",
          params: { content: params.message },
        });
      }
    });
  } else if (params.checkRadio === "2") {
    //等距插入
    let dist = lineLength;
    let towerName = 1;
    mapSDKType = "插入杆塔";
    while (dist > parseFloat(params.referenceDistance)) {
      poleNames.push({
        poleNum: `1${
          towerName === 1 ? "" : "+" + (towerName - 1)
        }#~1+${towerName}#`, //杆号=名字
        dist: parseFloat(params.referenceDistance), //距离
      });
      towerName++;
      dist -= parseFloat(params.referenceDistance);
    }
    const num = lineLength % parseFloat(params.referenceDistance);
    if (num < 3) {
      poleNames.pop();
    }
    // poleNames.push({
    //     poleNum: `1${towerName===1 ? '' : ('+' + (towerName-1)) }#~2#`,
    //     dist: dist,
    // })
    // poleNames.pop();
    console.log("🚀 ~ batchInsertPoles ~ poleNames:", poleNames);
    let options = {
      conductorLength: lineLength,
      poleSymbol: params.symbolId,
      insertPoleInfo: poleNames,
    };
    console.log("插入杆塔SDK入参", options);
    SDKDrawState.setCurrentDrawInfo("SDK_cgt", "batchInsertPoles", true);
    map.batchInsertPoles(options, (res) => {
      SDKDrawState.changeCurrentDrawInfo("batchInsertPoles", false);
      console.log("插入杆塔-batchInsertPoles-回调", res);
      if (res.status === "000000") {
        sendMessage({ type: "reply", res, mapSDKType, cmd: "SDK_cgt" });
      } else {
        sendMessage({
          type: "showMessage",
          params: { content: params.message },
        });
      }
    });
  } else if (params.checkRadio === "3") {
    //等分插入
    let towerNum = parseInt(params.referenceDistance);
    let towerName = 1;
    mapSDKType = "插入杆塔";
    let towerDist = lineLength / (towerNum + 1);
    while (towerNum > 0) {
      poleNames.push({
        poleNum: `1${
          towerName === 1 ? "" : "+" + (towerName - 1)
        }#~1+${towerName}#`,
        dist: towerDist,
      });
      towerName++;
      towerNum--;
    }
    // poleNames.push({
    //     poleNum: `1${towerName===1 ? '' : ('+' + (towerName-1)) }#~2#`,
    //     dist: towerDist,
    // })
    // poleNames.pop();
    let options = {
      conductorLength: lineLength,
      poleSymbol: params.symbolId,
      insertPoleInfo: poleNames,
    };
    console.log("插入杆塔SDK入参", options);
    SDKDrawState.setCurrentDrawInfo("SDK_cgt", "batchInsertPoles", true);
    map.batchInsertPoles(options, (res) => {
      SDKDrawState.changeCurrentDrawInfo("batchInsertPoles", false);
      console.log("插入杆塔-batchInsertPoles-回调", res);
      if (res.status === "000000") {
        sendMessage({ type: "reply", res, mapSDKType, cmd: "SDK_cgt" });
      } else {
        sendMessage({
          type: "showMessage",
          params: { content: params.message },
        });
      }
    });
  }
  // poleNames = [
  //     {
  //     name: "1#~1+1#",
  //     dist: 1,
  //     },
  //     {
  //         name: "1+1#~门架",
  //         dist: 2,
  //     },
  // ];
}

// 新建电缆线路
function addCableLine(map: any, options: object) {
  mapSDKType = "绘制电缆路径";
  console.log("addCableLine");
  SDKDrawState.setCurrentDrawInfo("SDK_add_cable_line", "addCableLine", true);
  map.addCableLine(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addCableLine", false);
    console.log("新建电缆线路响应结果：", params);
    if (params.status === "000000") {
      dealSDKDrawLineData(
        params,
        map,
        { type: "reply", params, mapSDKType, cmd: "SDK_add_cable_line" },
        ""
      );
    } else {
      sendMessage({ type: "showMessage", params: { content: params.message } });
    }
  });
}

//基于土建路径
function addSoilLine(map: any, options: object) {
  console.log("🚀 ~ addSoilLine ~ kfldf:", options);
  mapSDKType = "基于土建路径";
  if (options?.status === "000000") {
    let options2 = {
      isNewLine: true,
      newLineName: options.name,
      lineType: "2",
      selectedCableChannelIndex: 0,
      selectedCableJointIds: [],
      //   options.result.startItem.id
      //   options.result.endItem.id
      startInsidePointId:
        options.result.startItem.className == "Substation"
          ? options.result.cnInfo[0].START_ID
          : options.result.startItem.className != "PWPolePSR"
          ? options.result.startItem.id
          : "",
      endInsidePointId: options.result.endCnInfo
        ? options.result.endCnInfo[0].START_ID
        : options.result.startItem.className != "PWPolePSR"
        ? options.result.endItem.id
        : "",
    };
    console.log("🚀 ~ map.getCableCreateInfoFromPipe ~ options:", options2);
    map.drawCable(options2, (params) => {
      console.log("基于土建路径", params);
      sendMessage({
        type: "reply",
        params,
        mapSDKType,
        cmd: "SDK_add_soil_line",
      });
    });
  } else {
    map.getCableCreateInfoFromPipe((params) => {
      console.log("🚀 ~ map.getCableCreateInfoFromPipe ~ params:", params);
    });
  }
}

//管道增线
function addPiping(map: any, options: object) {
  mapSDKType = "管道增线";
  SDKDrawState.setCurrentDrawInfo("SDK_add_piping", "setExtendCable", true);
  map.setExtendCable((params) => {
    console.log("延长电缆到响应结果：", params);
    SDKDrawState.changeCurrentDrawInfo("setExtendCable", false);
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_add_piping" });
  });
}
async function extendCableLine(map: any, options: object) {
  console.log("extendCableLine");
  mapSDKType = "延长电缆路径";
  SDKDrawState.setCurrentDrawInfo(
    "SDK_extend_cable_line",
    "extendCableLine",
    true
  );

  try {
    // 第一步：延长电缆路径
    const params = await new Promise((resolve, reject) => {
      map.extendCableLine(options, (response) => {
        if (response.status === "000000") {
          resolve(response);
        } else {
          reject(new Error(response.message));
        }
      });
    });

    SDKDrawState.changeCurrentDrawInfo("extendCableLine", false);
    const ids = params.result.data.filter(
      (item) => item.CLASS_NAME === "PWCableSecPSR" && item.addFlag === true
    );

    if (
      ids.length > 0 &&
      (ids[0].START_DEV_CLASS_NAME === "PWCableJointPSR" ||
        ids[0].END_DEV_CLASS_NAME === "PWCableJointPSR")
    ) {
      // 第二步：获取设备信息
      const options2 = [
        {
          devId: ids[0].START_DEV_ID,
          className: ids[0].START_DEV_CLASS_NAME,
        },
      ];

      const response = await new Promise((resolve, reject) => {
        map.getDeviceInfo(options2, (res) => {
          if (res.status === "000000") {
            resolve(res);
          } else {
            reject(new Error(res.message));
          }
        });
      });

      if (response.result[0].INSTALL_SITE_ID) {
        // 第三步：电缆入沟
        const options1 = {
          cableSecId: ids[0].DEV_ID,
          chooseExistChannel: true,
          cableChanelInfo: {
            name: "tongdao",
            id: "",
          },
          direction: "1",
          wellStartNo: "1",
          prefix: "6",
          suffix: "%",
        };

        const channelResult = await new Promise((resolve, reject) => {
          map.cableGoInChannel(options1, (res) => {
            console.log("电缆入沟响应结果：", res);
            if (res.status === "000000") {
              resolve(res);
            } else {
              reject(new Error(res.message));
            }
          });
        });
      }
    }

    // 处理绘图数据
    dealSDKDrawLineData(
      params,
      map,
      { type: "reply", params, mapSDKType, cmd: "SDK_extend_cable_line" },
      ""
    );
  } catch (error) {
    console.error("延长电缆路径出错:", error);
    sendMessage({
      type: "showMessage",
      params: { content: error.message },
    });
  }
}
//插入电缆头-绘制中间接头
function extendCableDlt(map: any, options: object) {
  mapSDKType = "电缆中间接头";
  SDKDrawState.setCurrentDrawInfo(
    "SDK_extend_cable_dlt",
    "addCableDevice",
    true
  );
  map.addCableDevice(options, (params) => {
    if (params.status === "000000") {
      SDKDrawState.changeCurrentDrawInfo("addCableDevice", false);
      const sdkID = params.result.data.find((value) => {
        return value.CLASS_NAME === "PWCableSecPSR" && !value.LENGTH;
      });
      const sdkID1 = params.result.data.find((value) => {
        return value.CLASS_NAME === "PWCableSecPSR" && value.LENGTH;
      });
      const options1 = [
        {
          devId: sdkID.DEV_ID,
          className: "PWCableSecPSR",
        },
      ];
      map.getDeviceInfo(options1, (response) => {
        if (response.status === "000000") {
          response.result[0].LENGTH = response.result[0].LENGTH - sdkID1.LENGTH;
          delete response.result[0].SRID;
          map.setDeviceInfo(response.result, (params) => {
            console.log("保存设备信息数据响应结果：", params);
          });
          params.result.data.forEach((item) => {
            if (item.CLASS_NAME === "PWCableSecPSR" && !item.LENGTH) {
              item.LENGTH = response.result[0].LENGTH;
            }
          });
          sendMessage(params);
        }
      });
    }
  });
}

//插入电缆头- 等分插入接头
function equallyConnector(map: any, options: object) {
  mapSDKType = "等分插入接头";
  SDKDrawState.setCurrentDrawInfo(
    "SDK_equidistant_connector",
    "eqInsertCableJoint",
    true
  );
  map.eqInsertCableJoint(options, (params) => {
    if (params.status == "error") {
      sendMessage({ type: "showMessage", params: { content: params.message } });
      return;
    }
    SDKDrawState.changeCurrentDrawInfo("eqInsertCableJoint", false);
    sendMessage({
      type: "reply",
      params,
      mapSDKType,
      cmd: "SDK_equidistant_connector",
    });
  });
}
//插入电缆头- 等距插入接头
function equidistantConnector(map: any, options: object) {
  mapSDKType = "等距插入接头";
  SDKDrawState.setCurrentDrawInfo(
    "SDK_equally_connector",
    "equidInsertCableJoint",
    true
  );
  map.equidInsertCableJoint(options, (params) => {
    if (params.status == "error") {
      sendMessage({ type: "showMessage", params: { content: params.message } });
      return;
    }
    SDKDrawState.changeCurrentDrawInfo("equidInsertCableJoint", false);
    sendMessage({
      type: "reply",
      params,
      mapSDKType,
      cmd: "SDK_equally_connector",
    });
  });
}
//绘制电缆分支箱
function addCableBox(map: any, options: object) {
  mapSDKType = "绘制电缆分支箱";
  console.log("绘制电缆分支箱SDK入参：", options);
  SDKDrawState.setCurrentDrawInfo("SDK_dlfzx", "addCableBox", true);
  map.addStation(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addCableBox", false);
    console.log(mapSDKType, params);
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_dlfzx" });
  });
}

// 绘制站房
function addStation(map: any, options: any) {
  mapSDKType = "新建站房";
  console.log("绘制站房SDK入参：", options);
  // let isTemplates = false
  // if(options.sdkStationId){
  //     //获取当前站房模块里是否存在 sdk对应方案，如果存在查询数据 调用应用典型站房功能
  //     SDKDrawState.setCurrentDrawInfo('SDK_zfty', 'searchPrintTemplates', true)
  //     map.searchPrintTemplates((params) => {
  //         SDKDrawState.changeCurrentDrawInfo('searchPrintTemplates', false)
  //         console.log("获取典型站房", params);
  //         if(params.status === "000000"){
  //             const typicalStation = params.result.find(o=>o.id === options.sdkStationId)
  //             if(typicalStation){
  //                 isTemplates = true
  //                 SDKDrawState.setCurrentDrawInfo('SDK_zfty', 'applyPrintTemplates', true)
  //                 let applyOptions = {
  //                     volId: options.voltageLevel + '',
  //                     aliasname: options.stationName,
  //                     name: options.stationName,
  //                     typicalStation
  //                 };
  //                 map.applyPrintTemplates(applyOptions, (params) => {
  //                     SDKDrawState.changeCurrentDrawInfo('applyPrintTemplates', false)
  //                     console.log("应用典型站房", params);
  //                     sendMessage({type: 'reply', params, mapSDKType, cmd: "SDK_zfty"})
  //                 });
  //             }
  //         }
  //     })
  // }
  // if(!isTemplates) {
  addStationNoTemplates(map, options);
  // }
}
// 非调用典设方案 的站房绘制
const addStationNoTemplates = (map: any, options: any) => {
  SDKDrawState.setCurrentDrawInfo("SDK_zfty", "addStation", true);
  map.addStation(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("addStation", false);
    console.log("新建站房：", params);
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_zfty" });
  });
};
// 绘制工井
const insertWell = (map: any, options: any) => {
  mapSDKType = "绘制工井";
  console.log("绘制工井SDK入参：", options);
  SDKDrawState.setCurrentDrawInfo("SDK_well", "insertWell", true);
  map.insertWell(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("insertWell", false);
    console.log("插入工井响应结果", params);
    sendMessage({ type: "reply", params, mapSDKType, cmd: "SDK_well" });
  });
};

// 工程复用
function versionExtend(map: any, options: object) {
  mapSDKType = "成果复用";
  SDKDrawState.setCurrentDrawInfo("SDK_versionExtend", "versionExtend", true);
  map.versionExtend(options, (params) => {
    SDKDrawState.changeCurrentDrawInfo("versionExtend", false);
    console.log("图模复用响应结果", params);
    sendMessage({
      type: "reply",
      params,
      mapSDKType,
      cmd: "SDK_versionExtend",
    });
  });
}
// 撤销
function undo(map: any, options: object) {
  map.undo((params) => {
    console.log("撤销响应结果", params);
    sendMessage({ type: "reply", params, cmd: "SDK_undo" });
  });
}
// 重做
function redo(map: any, options: object) {
  map.redo((params) => {
    console.log("重做响应结果", params);
    sendMessage({ type: "reply", params, cmd: "SDK_redo" });
  });
}
// 删除
function removeDevice(map: any, options: object) {
  map.removeDevice([], (params) => {
    console.log("删除响应结果", params);
    sendMessage({ type: "reply", params, cmd: "SDK_removeDevice" });
  });
}
