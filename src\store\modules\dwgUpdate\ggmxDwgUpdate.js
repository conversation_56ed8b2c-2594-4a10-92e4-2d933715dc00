import {
    getGggMx, generatorGggmxReport
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
const ggmxDwgUpdate = defineStore(
    'ggmxDwgUpdate',
    {
        state: () => ({
            tableData: undefined,
        }),
        actions: {
            getData(taskId) {
                return new Promise((resolve, reject) => {
                    getGggMx(taskId).then(res => {
                        // console.log("🚀 ~ getGggMx ~ res:", res)
                        try {
                            if (res.code == 200) {
                                this.tableData = res.data
                                resolve()
                            }
                        } catch (error) {
                            resolve(error)
                        }
                    })
                })
            },
            async dwgUpdateFun(th) {
                publicStore().oniframeMessage({
                    type: "cadPreview",
                    content: "Mx_gggcl",
                    formData: {
                        content: "钢管杆明细表",
                        tableData: JSON.stringify(this.tableData),
                        countyOrganisationName: new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName'),
                        projectName: new URLSearchParams(new URL(window.location.href).search).get('projectName',),
                        stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                        proCode: new URLSearchParams(new URL(window.location.href).search).get('proCode'),
                        th: th
                    },
                });
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        const files = event.data.params.formData.files
                        const tableData = JSON.parse(event.data.params.formData.tableData)
                        const tableDataNew = tableData.map(item => {
                            return {
                                gx: item.RodType,
                                djzz: item.GrossWeightOfSingleBase,
                                gtyd: item.GD1,
                                lsjddqz: item.DJLS,
                                gtedzz: item.GD2,
                                ptz: item.PTZL,
                                hdzz: item.CrossArm,
                                js: item.Base,
                                zz: item.FinalGrossWeight
                            }
                        })
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('multipartFile', item)
                        })
                        fileFormData.append("gggmxList", JSON.stringify(tableDataNew));
                        fileFormData.append('prjTaskInfoId', new URLSearchParams(new URL(window.location.href).search).get('id'))
                        fileFormData.append('stage', new URLSearchParams(new URL(window.location.href).search).get('stage'))
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        generatorGggmxReport(fileFormData).then(res => {
                            if (res.code == 200) resolve()
                            // if (res.code === 200) {
                            //     proxy.$message.success("保存成功");
                            //     loading.close()
                            //     if (callChildC) {
                            //         callChildC();
                            //     }
                            // } else {
                            //     proxy.$message.error(res.msg);
                            //     loading.close()
                            // }
                        })
                    } catch (error) {
                        reject(error)
                    }

                })


            }

        },
        persist: true
    })
export default ggmxDwgUpdate
