<template>
  <!-- 二.电缆线路设计 -->
  <!-- 1.绘制电缆路径 -->
  <data-dialog
    dataWidth="690"
    @close="closeDialog"
    v-if="appStore.mapIndex == '绘制电缆路径'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        绘制电缆路径
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 通道类别 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            :disabled="flag.tdLb"
            @change="handleChannelClass"
            v-model="cableForm.channelClass"
            placeholder="请选择"
          >
            <el-option label="排管" value="PG"></el-option>
            <el-option label="电缆沟" value="DLG"></el-option>
            <el-option label="非开挖拉管" value="FKWLG"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 通道型号 </span>
        </div>
        <div class="line-input">
          <el-select
            :disabled="flag.tdType"
            class="in-item"
            v-model="cableForm.channelType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in channelTypeList"
              :label="item.modulename"
              :value="item.moduleid"
              :key="item.moduleid"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 通道状态 </span>
        </div>
        <div class="line-input">
          <el-select
            :disabled="flag.tdStatus"
            class="in-item"
            v-model="cableForm.channelStatus"
            placeholder="请选择"
          >
            <el-option label="新建" value="New"></el-option>
            <el-option label="原有" value="Original"></el-option>
          </el-select>
        </div>
      </div>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div class="title-color"></div>
        <div class="title-text">电缆设置</div>
        <div
          style="
            background: #fff;
            border: 1px solid #3c3e46;
            border-radius: 30px;
            color: #3c3e46;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
            font-weight: bold;
          "
        >
          <el-button
            @click="cableAdd"
            :disabled="flag.addFlag"
            size="small"
            type="text"
            >添加
          </el-button>
        </div>
      </div>
      <div class="line">
        <el-table class="input-table" size="small" :data="cableData" border>
          <el-table-column
            label="线路名称"
            align="center"
            prop="name"
            width="150"
          >
            <template #default="scope">
              <!--              <el-input
                              style="height: 27px"
                              v-model="scope.row.xlName"
                              placeholder=""
                            ></el-input>-->
              <el-select
                filterable
                allow-create
                default-first-option
                :reserve-keyword="false"
                size="small"
                style="width: 100px"
                v-model="scope.row.xlName"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in lineOption.tableNameOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="电缆型号" align="center" prop="key" width="">
            <template #default="scope">
              <el-select
                style="width: 90% !important; height: 32px !important"
                v-model="scope.row.dlxh"
              >
                <el-option
                  v-for="item in cableTypeList"
                  :label="item.modulename"
                  :value="item.moduleid"
                  :key="item.moduleid"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="sk" width="100">
            <template #default="scope">
              <el-select
                style="width: 90% !important; height: 32px !important"
                v-model="scope.row.status"
              >
                <el-option label="新建" value="New"></el-option>
                <el-option label="原有" value="Original"></el-option>
                <el-option label="拆除" value="Remove"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <!--          <el-table-column
                      label="馈线名称"
                      align="center"
                      prop="sk"
                      width="120"
                    >
                      <template #default="scope">
                        <el-input
                          style="height: 27px"
                          v-model="scope.row.kxName"
                          placeholder=""
                        ></el-input>
                      </template>
                    </el-table-column>-->
          <el-table-column label="终端头" align="center" width="70">
            <template #default="scope">
              <el-checkbox
                :disabled="flag.zdtFlag"
                v-model="scope.row.zdt"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column align="center" label="删除" width="70">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                :disabled="flag.delFlag"
                @click="deleteRow(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div class="title-color"></div>
        <div class="title-text">绘制设置</div>
      </div>

      <div class="line">
        <div class="line-item">
          <span> 绘制方式 </span>
        </div>
        <div class="line-input">
          <el-radio-group class="radio-group" v-model="cableForm.checkBox">
            <el-radio value="1" size="small">绘制电缆</el-radio>
            <el-radio style="margin-right: 14px" value="2" size="small"
              >继续绘制</el-radio
            >
            <div
              style="
                margin-right: 14px;
                background: white;
                width: 2px;
                height: 40px;
              "
            ></div>
            <el-radio value="3" size="small">基于土建路径</el-radio>
            <!-- <el-radio value="4" size="small">管道增线</el-radio> -->
          </el-radio-group>
          <!-- <el-checkbox-group
            class="checkbox-group"
            style="display: flex"
            @change="handleDrawType"
            v-model="cableForm.checkBox"
          >
            <el-checkbox
              :disabled="flag.jxFlag"
              size="small"
              label="继续绘制"
              value="1"
            />
            <el-checkbox
              :disabled="flag.jytjFlag"
              size="small"
              label="基于土建路径"
              value="3"
            />
            <el-checkbox
              :disabled="flag.hzdljFlag"
              size="small"
              label="绘制电缆井"
              value="4"
            />
          </el-checkbox-group> -->
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆绘制间距 </span>
        </div>
        <div class="line-input">
          <el-input
            :disabled="flag.jjFlag"
            class="in-item"
            min="0"
            type="number"
            v-model="cableForm.cableDistance"
            placeholder="请输入"
          ></el-input>
        </div>
      </div>
      <!-- <div class="line-prefix">
        <div class="line-item">
          <span> 电缆井编号规则 </span>
        </div>
        <div class="line-input">
          <el-input
            :disabled="flag.qzFlag"
            class="in-item"
            v-model="cableForm.prefix"
            placeholder="前缀"
          ></el-input>
          <el-input
            :disabled="flag.contentFlag"
            class="in-item"
            v-model="cableForm.content"
            placeholder=""
          ></el-input>
          <el-input
            :disabled="flag.hzFlag"
            class="in-item"
            v-model="cableForm.suffix"
            placeholder="后缀"
          ></el-input>
        </div>
      </div> -->
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="drawCableLineChange">绘制</div>
      </div>
    </template>
  </data-dialog>

  <!-- 2.插入电缆头 -->
  <data-dialog
    dataWidth="420px"
    @close="closeDialog"
    v-if="appStore.mapIndex == '插入电缆头'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        插入电缆头
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item" style="height: 70px">
          <span> 电缆头绘制类型 </span>
        </div>
        <div class="radio-input" style="height: 70px">
          <el-radio-group v-model="cableForm1.dltLx" @change="dltLxChange">
            <div
              style="
                width: 100%;
                display: flex;
                align-items: center;
                padding: 0 10px;
              "
            >
              <el-radio style="width: 20%" value="1" label="中间头"></el-radio>
              <el-select
                style="width: 80%"
                class="in-item"
                v-model="cableForm1.cable1"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in zjtList"
                  :label="item.modulename"
                  :value="item.moduleid"
                  :key="item.moduleid"
                  :tiele="item.modulename"
                >
                </el-option>
              </el-select>
            </div>
            <div
              style="
                width: 100%;
                display: flex;
                align-items: center;
                padding: 0 10px;
              "
            >
              <el-radio style="width: 20%" value="2" label="终端头"></el-radio>
              <el-select
                style="width: 80%"
                class="in-item"
                ref="selectRef"
                @focus="changeRef"
                v-model="cableForm1.cable2"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in zdtList"
                  :label="item.modulename"
                  :value="item.moduleid"
                  :key="item.moduleid"
                  :title="item.modulename"
                >
                </el-option>
              </el-select>
            </div>
          </el-radio-group>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆头插入方式 </span>
        </div>
        <div class="line-input">
          <el-radio-group
            @change="handleInsertType"
            class="radio-group"
            v-model="cableForm1.checkRadio"
            style="flex-wrap: nowrap"
          >
            <el-radio
              style="margin-right: 10px"
              :disabled="cableHeadData.zyFlag"
              value="1"
              label="自由插入"
            ></el-radio>
            <el-radio
              style="margin-right: 10px"
              :disabled="cableHeadData.dfFlag"
              value="2"
              label="等分插入"
            ></el-radio>
            <el-radio
              style="margin-right: 10px"
              :disabled="cableHeadData.djFlag"
              value="3"
              label="等距插入"
            ></el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆头数量 </span>
        </div>
        <div class="line-input">
          <el-input
            type="number"
            min="1"
            class="in-item"
            :disabled="cableHeadData.dltNumFlag"
            v-model="cableForm1.cableHeadNum"
            placeholder=""
          ></el-input>
          <span style="color: #282b33; font-size: 14px; font-weight: 400">
            个
          </span>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆头间距 </span>
        </div>
        <div class="line-input">
          <el-input
            type="number"
            min="1"
            class="in-item"
            :disabled="cableHeadData.dltDistanceFlag"
            v-model="cableForm1.cableHeadDistance"
            placeholder=""
          ></el-input>
          <span style="color: #282b33; font-size: 14px; font-weight: 400">
            米
          </span>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="dltClick" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>

  <!-- 3.绘制电缆分支箱 -->
  <data-dialog
    @close="closeDialog"
    v-if="appStore.mapIndex == '绘制电缆分支箱'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        增加电缆分支箱
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 电压等级 </span>
        </div>
        <div class="line-input">
          <el-select
            @change="voltageLevelChange"
            class="in-item"
            v-model="cableForm2.voltageLevel"
          >
            <el-option
              v-for="item in cableForm2.voltageLevelOption"
              :label="item.text"
              :value="item.text"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆分支箱类别 </span>
        </div>
        <div class="line-input">
          <el-select
            @change="cableBoxLbChange"
            v-model="cableForm2.cableBoxType"
            class="in-item"
          >
            <el-option
              v-for="item in dlfzxLbList"
              :label="item.moduletypename"
              :value="item.moduletypekey"
              :key="item.moduletypekey"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆分支箱 </span>
        </div>
        <div class="line-input">
          <el-select
            v-model="cableForm2.cableBox"
            @change="cableBoxModuleChange"
            class="in-item"
          >
            <el-option
              v-for="item in dlfzxList"
              :label="item.modulename"
              :value="item.moduleid"
              :key="item.moduleid"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select class="in-item" v-model="cableForm2.status">
            <el-option label="新建" value="New"></el-option>
            <el-option label="原有" value="Original"></el-option>
            <el-option label="拆除" value="Remove"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 设备编号 </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm2.deviceCode"
            placeholder="请输入设备编号"
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 线路名称 </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm2.lineName"
            placeholder="请输入线路名称"
          ></el-input>
        </div>
      </div>
      <div
        class="line"
        v-for="(item, index) in cableForm2.lineInIntervals"
        :key="index"
      >
        <div class="line-item">
          <span> 进线间隔 {{ index + 1 }} </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm2.lineInIntervals[index]"
            placeholder="间隔名称"
          ></el-input>
        </div>
      </div>
      <div
        class="line"
        v-for="(item, index) in cableForm2.lineOutIntervals"
        :key="index"
      >
        <div class="line-item">
          <span> 出线间隔 {{ index + 1 }} </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm2.lineOutIntervals[index]"
            placeholder="间隔名称"
          ></el-input>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="drawCableBox">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 4.绘制电缆通道 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '绘制电缆通道'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        绘制电缆通道
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 线路名称 </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm3.lineName"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 敷设方式 </span>
        </div>
        <div class="line-input">
          <el-select
            @change="handleLayMethod"
            class="in-item"
            v-model="cableForm3.layMethod"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.gcList"
              :label="item.moduletypename"
              :value="item.moduletypekey"
              :key="item.moduletypekey"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 通道型号 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm3.channelModel"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.tdlxList"
              :label="item.modulename"
              :value="item.moduleid"
              :key="item.moduleid"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select class="in-item" v-model="cableForm3.status">
            <el-option label="新建" value="New"></el-option>
            <el-option label="原有" value="Original"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 土质 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm3.soil"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.tzlxList"
              :label="item.value"
              :value="item.key"
              :key="item.key"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div
        v-if="channelTube !== '2'"
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div
          style="width: 3px; height: 20px; background: #07888a; margin: 0 10px"
        ></div>
        <div class="title-text">电缆保护管</div>
      </div>

      <div v-if="channelTube !== '2'" class="line">
        <div class="line-item">
          <span> 管材类型 </span>
        </div>
        <div class="line-input">
          <el-select
            @change="handlePipeType1"
            class="in-item"
            v-model="cableForm3.pipeType1"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.gclxList"
              :label="item"
              :value="item"
              :key="item"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div v-if="channelTube !== '2'" class="line">
        <div class="line-item">
          <span> 管材型号 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm3.pipeModel1"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.gcxhList"
              :label="item.modulename"
              :value="item.moduleid"
              :key="item.moduleid"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div
        v-if="channelTube == '0'"
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div
          style="width: 3px; height: 20px; background: #07888a; margin: 0 10px"
        ></div>
        <div class="title-text">通讯管选择</div>
      </div>
      <div v-if="channelTube == '0'" class="line">
        <div class="line-item">
          <span> 管材类型 </span>
        </div>
        <div class="line-input">
          <el-select
            @change="handlePipeType2"
            class="in-item"
            v-model="cableForm3.pipeType2"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.gclxList"
              :label="item"
              :value="item"
              :key="item"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div v-if="channelTube == '0'" class="line">
        <div class="line-item">
          <span> 管材型号 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm3.pipeModel2"
            placeholder="请选择"
          >
            <el-option
              v-for="item in hzdlList.gcxhList1"
              :label="item.modulename"
              :value="item.moduleid"
              :key="item.moduleid"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div
          style="width: 3px; height: 20px; background: #07888a; margin: 0 10px"
        ></div>
        <div class="title-text">电缆井参数</div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 破碎路面 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm3.brokenRoad"
            placeholder="请选择"
          >
            <el-option label="混凝土路面" value="混凝土路面"></el-option>
            <el-option
              label="沥青混凝土路面"
              value="沥青混凝土路面"
              title="沥青混凝土路面"
            ></el-option>
            <el-option
              label="砖石路面（包括碎石、砖块、花岗岩等）"
              value="砖石路面（包括碎石、砖块、花岗岩等）"
              title="砖石路面（包括碎石、砖块、花岗岩等）"
            ></el-option>
            <el-option
              label="普通草坪修复"
              title="普通草坪修复"
              value="普通草坪修复"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 破碎路面(m²) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            type="number"
            min="0"
            v-model="cableForm3.brokenRoadArea"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 60px;
          display: flex;
          margin-bottom: 1px;
          flex-direction: column;
        "
      >
        <div style="display: flex; align-items: center">
          <div
            style="
              width: 3px;
              height: 20px;
              background: #07888a;
              margin: 0 10px;
            "
          ></div>
          <div class="title-text">通道节点</div>
        </div>
        <div style="margin-left: 10px">
          <el-checkbox-group
            @change="channelChange"
            v-model="cableForm3.checkBox"
          >
            <el-checkbox
              disabled
              checked
              class="check_radio"
              label="自动匹配电缆井"
            ></el-checkbox>
            <!-- <el-checkbox
              checked
              :disabled="cableRules"
              class="check_radio"
              label="起始电缆井"
            ></el-checkbox> -->
          </el-checkbox-group>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 绘制设置 </span>
        </div>
        <div class="line-input">
          <el-checkbox
            disabled
            @change="checkRadioChange"
            v-model="cableForm3.checkRadio"
            class="check_radio"
            label="快速绘制"
          ></el-checkbox>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆井数量 </span>
        </div>
        <div class="line-input">
          <el-input
            :disabled="cableNumFlag || cableRules"
            class="in-item"
            v-model="cableForm3.cableNum"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆井间距(m) </span>
        </div>
        <div class="line-input">
          <el-input
            :disabled="cableNumFlag || cableRules"
            class="in-item"
            v-model="cableForm3.cableDistance"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line-prefix">
        <div class="line-item">
          <span> 电缆井编号规则 </span>
        </div>
        <div class="line-input">
          <el-input
            :disabled="cableRules"
            class="in-item"
            v-model="cableForm3.cableCode1"
            placeholder="前缀"
          ></el-input>
          <el-input
            class="in-item"
            :disabled="cableRules"
            type="number"
            min="0"
            v-model="cableForm3.cableCode2"
            placeholder=""
          ></el-input>
          <el-input
            :disabled="cableRules"
            class="in-item"
            v-model="cableForm3.cableCode3"
            placeholder="后缀"
          ></el-input>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div @click="drawDltd" class="line-bnt">绘制</div>
      </div>
    </template>
  </data-dialog>
  <!-- 5.绘制电缆井 -->
  <data-dialog @close="closeDialog" v-if="appStore.mapIndex == '绘制电缆井'">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        绘制电缆井
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span>线路名称</span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm4.lineName"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 类型 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            @change="changeType"
            v-model="cableForm4.type"
            placeholder="请选择"
          >
            <el-option
              v-for="item in dlList.lxList"
              :label="item.moduletypename"
              :value="item.moduletypekey"
              :key="item.moduletypekey"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 电缆井型号 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm4.cableModel"
            placeholder="请选择"
          >
            <el-option
              v-for="item in dlList.dljxhList"
              :label="item.modulename"
              :value="item.moduleid"
              :key="item.moduleid"
            >
              <el-tooltip
                :content="item.modulename"
                placement="top"
                effect="dark"
              >
                <span>{{ item.modulename }}</span>
              </el-tooltip>
            </el-option>
          </el-select>
        </div>
      </div>

      <div class="line">
        <div class="line-item">
          <span>工井编号</span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            v-model="cableForm4.userNumber"
            placeholder=""
          ></el-input>
        </div>
      </div>

      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select class="in-item" v-model="cableForm4.status">
            <el-option label="新建" value="New"></el-option>
            <el-option label="原有" value="Original"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 土质 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm4.soil"
            placeholder="请选择"
          >
            <el-option
              v-for="item in dlList.tzlxList"
              :label="item.value"
              :value="item.key"
              :key="item.key"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 破碎路面 </span>
        </div>
        <div class="line-input">
          <el-select
            class="in-item"
            v-model="cableForm4.brokenRoad"
            placeholder="请选择"
          >
            <el-option label="混凝土路面" value="混凝土路面"></el-option>
            <el-option
              label="沥青混凝土路面"
              value="沥青混凝土路面"
            ></el-option>
            <el-option
              label="砖石路面（包括碎石、砖块、花岗岩等）"
              value="砖石路面（包括碎石、砖块、花岗岩等）"
            ></el-option>
            <el-option label="普通草坪修复" value="普通草坪修复"></el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 破碎路面(m²) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            min="0"
            type="number"
            v-model="cableForm4.brokenRoadArea"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div style="background: #e4f2f2; height: 50px" class="line">
        <div class="line-bnt" @click="drawWell">绘制</div>
      </div>
    </template>
  </data-dialog>

  <!-- 6通道内敷设位置图 -->
  <data-dialog
    @close="closeDialog"
    v-if="appStore.mapIndex == '通道内敷设位置图'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        通道内敷设位置图
      </h4>
    </template>
    <template #body>
      <div>
        <div class="line">
          <div class="line-item">
            <span> 电缆根数 </span>
          </div>
          <div class="line-input" style="font-weight: normal">
            <span style="background: white" class="in-item">{{
              cableNum?.length
            }}</span>
          </div>
        </div>
        <div class="line">
          <div class="line-item">
            <span> 通道型号 </span>
          </div>
          <div class="line-input" style="font-weight: normal">
            <span style="background: white" class="in-item">{{
              cableType.length > 0 ? cableType[0]?.AttributeValue : ""
            }}</span>
          </div>
        </div>
        <div class="line" style="font-weight: normal">
          <el-table class="input-table" :data="cableList" border>
            <el-table-column label="管孔编号" align="center">
              <template #default="scope">
                <span>{{ parseInt(scope.row.NO) + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="电缆型号"
              show-overflow-tooltip
              align="center"
              prop="xh"
            >
            </el-table-column>
            <el-table-column
              label="线路名称"
              show-overflow-tooltip
              align="center"
              prop="NAME"
            >
            </el-table-column>
            <el-table-column label="状态" show-overflow-tooltip align="center">
              <template #default="scope">
                <span v-if="scope.row.state == 'New'">新建</span>
                <span v-else-if="scope.row.state == 'Original'">原有</span>
                <span v-else-if="scope.row.state == 'Remove'">拆除</span>
                <span v-else></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          style="
            background: #e4f2f2;
            height: 50px;
            justify-content: space-around;
          "
          class="line"
        >
          <div @click="openProfile" class="line-bnt">打开剖面</div>
          <div @click="saveProfile" style="width: 120px" class="line-bnt">
            剖面编辑数据保存
          </div>
          <div @click="generateDraw" class="line-bnt">生成图纸</div>
        </div>
        <div>
          <span style="color: red"
            >注：请先选中电缆通道后再打开剖面，然后创建剖面，最后剖面编辑数据保存（只会生成最新创建的剖面信息）</span
          >
        </div>
      </div>
    </template>
  </data-dialog>

  <tower-select
    v-model:visible="towerSelectVisible"
    @updateParams="updateTowerParams"
  />
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import towerSelect from "./tower-select.vue";
import useAppStore from "@/store/modules/app.js";
import { getDicts, getEngineerLine } from "@/api/desginManage/GccsDialog.js";
import {
  getChannelValue,
  getCableTypeFull,
  getCableHeadType,
  getModuleTypeKeyList,
  getCableBranchBoxList,
  getCableWellType,
  getCableWellTypeNumber,
  moduleTypes,
  getModulesByModuleTypeKey,
  pipeTypes,
  pipeModels,
  getCableBoxInOutData,
} from "@/api/baseDate/onlineDesign";
import { useRoute } from "vue-router";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { reactive, ref } from "vue";
import { getPropertyData } from "@/api/onlineDesign/index.js";
import { voltageLevelData } from "@/views/pages/online/commonData.js";
import {
  drawEndCableBox,
  drawEndWell,
  drawEndCable,
  cableSoil,
} from "../sdkDrawBackData/cableModuleSDK";
import { cableHead } from "../sdkDrawBackData/cableHead1.js";
import { drawCableChannel } from "../sdkDrawBackData/drawCableChannel.js";
import {
  getUnderpassLayLocation,
  intervalAddFile,
  selectCableMiddleHead,
} from "@/api/insertSag/index.js";
const { sendMessage, cleanup } = useIframeCommunication();

const route = useRoute();
console.log("route", route);
const callChildC = inject("callChildC");
const taskId = route.query.id;

const { proxy } = getCurrentInstance();
const appStore = useAppStore();
//  二.电缆线路设计
//  定义 1.绘制电缆路径 form表单
const cableForm = ref({
  channelClass: "", //通道类别
  channelType: "", //通道类型
  channelStatus: "", //通道状态
  checkBox: "1", //绘制方式
  cableDistance: "", //电缆绘制间距
  prefix: "", //前缀
  suffix: "", //后缀
  content: "", //中间内容
});
//定义 2.插入电缆头 form表单
const cableForm1 = ref({
  dltLx: "1", //电缆头绘制类型
  cable1: "", //中间头-1
  cable2: "", //终端头-2
  checkRadio: "1", //电缆头插入方式
  cableHeadNum: "", //电缆头数量
  cableHeadDistance: "", //电缆头间距
});
//定义 绘制电缆分支箱 form表单
const cableForm2 = ref({
  voltageLevelOption: voltageLevelData.filter(
    (o) => o.id === "0" || o.id === "1" || o.id === "2"
  ),
  voltageLevel: "", //电压等级
  cableBoxType: "", //电缆分支箱类别
  cableBox: "", //电缆分支箱
  status: "", //状态
  deviceCode: "", //设备编号
  lineName: "", //线路名称
  lineInIntervals: [], //进线间隔
  lineOutIntervals: [], //出线间隔
});
//定义 3.绘制电缆通道 form表单
const cableForm3 = ref({
  lineName: "", //线路名称
  layMethod: "", //敷设方式
  channelModel: "", //通道型号
  status: "", //状态
  soil: "", //土质
  pipeType1: "", //管材类型1
  pipeType2: "", //管材类型2
  pipeModel1: "", //管材型号1
  pipeModel2: "", //管材型号2
  brokenRoad: "", //破碎路面
  brokenRoadArea: "", //破碎路面(m²)
  checkBox: [], //通道节点
  checkRadio: "", //快速绘制
  cableNum: "", //电缆并数量
  cableDistance: "", //电缆并间距
  cableCode1: "", //电缆编号规则1
  cableCode2: "", //电缆编号规则2
  cableCode3: "", //电缆编号规则3
});
//定义 绘制电缆井 form表单
const cableForm4 = ref({
  lineName: "", //线路名称
  type: "", //类型
  cableModel: "", //电缆井型号
  status: "", //状态
  soil: "", //土质
  brokenRoad: "", //破碎路面
  brokenRoadArea: "", //破碎路面(m²)
});
//定义 6通道内敷设位置图 form表单
const cableForm5 = ref({
  fNumberCables: "", //终期电缆根数
  channelModel: "", //通道型号
});

// !绘制电缆 ----start
const flag = ref({
  tdLb: false, //通道类型开关
  tdType: false, //通道类型开关
  tdStatus: false, //通道状态开关
  jjFlag: false, //电缆绘制间距开关
  qzFlag: true, //前缀开关
  hzFlag: true, //后缀开关
  contentFlag: true, //前缀 后缀中间内容开关
  zdtFlag: true, //终端头开关
  delFlag: false, //删除开关
  jxFlag: false, //继续绘制
  zgFlag: false, //主干绘制
  jytjFlag: false, //基于土建路径
  hzdljFlag: false, //绘制电缆井
  addFlag: false, //增加开关
});
const cableTypeList = ref([]);

// 绘制方式
const handleDrawType = (e) => {
  e.sort((a, b) => Number(b) - Number(a));
  // 初始化默认的 flag 设置
  const defaultFlag = {
    tdLb: false, // 通道类型开关
    tdType: false, // 通道类型开关
    tdStatus: false, // 通道状态开关
    jjFlag: false, // 电缆绘制间距开关
    qzFlag: true, // 前缀开关
    hzFlag: true, // 后缀开关
    contentFlag: true, // 前缀 后缀中间内容开关
    zdtFlag: true, // 终端头开关
    delFlag: false, // 删除开关
    jxFlag: false, // 继续绘制
    zgFlag: false, // 主干绘制
    jytjFlag: false, // 基于土建路径
    hzdljFlag: false, // 绘制电缆井
    addFlag: false, // 增加开关
  };
  if (e.length > 0) {
    // 判断目标数组中的元素是否都在 e 中
    const targetValues = ["1", "4"];
    const targetValues1 = ["3", "4"];
    const containsAll = targetValues.every((value) => e.includes(value));
    const containsAll1 = targetValues1.every((value) => e.includes(value));

    // 设置默认值
    flag.value = { ...defaultFlag };

    // 根据 e[0] 的值判断设置 flag
    const firstElement = e[0];

    const flagMap = {
      1: () => {
        flag.value.tdStatus = true;
        flag.value.qzFlag = true;
        flag.value.jjFlag = true;
        flag.value.hzFlag = true;
        flag.value.contentFlag = true;
        flag.value.zgFlag = true;
        flag.value.jytjFlag = true;
        flag.value.addFlag = true;
        flag.value.delFlag = true;
      },
      3: () => {
        flag.value.tdType = true;
        flag.value.tdLb = true;
        flag.value.tdStatus = true;
        flag.value.qzFlag = true;
        flag.value.jjFlag = false;
        flag.value.hzFlag = true;
        flag.value.contentFlag = true;
        flag.value.zdtFlag = true;
        flag.value.delFlag = true;
        flag.value.jxFlag = true;
        flag.value.hzdljFlag = true;
      },
      4: () => {
        flag.value.qzFlag = false;
        flag.value.contentFlag = false;
        flag.value.hzFlag = false;
      },
    };

    // 如果有对应的设置函数，调用
    if (flagMap[firstElement]) {
      flagMap[firstElement]();
    }

    // 如果满足 targetValues 数组的条件
    if (containsAll) {
      console.log("包含所有目标值", containsAll);
      flag.value.qzFlag = false;
      flag.value.jjFlag = false;
      flag.value.hzFlag = false;
      flag.value.contentFlag = false;
    }

    // 如果满足 targetValues1 数组的条件
    if (containsAll1) {
      flag.value.tdType = true;
      flag.value.tdLb = true;
      flag.value.tdStatus = true;
      flag.value.addFlag = true;
      flag.value.delFlag = true;
      flag.value.zdtFlag = true;
      flag.value.jxFlag = true;
    }
  } else {
    // 当 e 数组为空时，设置默认值
    flag.value = { ...defaultFlag };
  }
};
const cableData = ref([
  {
    xlName: "线路1",
    dlxh: "xjsdjfdg",
    status: "New",
    kxName: "馈线名称1",
    zdt: true,
  },
]);
//增加绘制电缆路径列表cableData  一行
const cableAdd = () => {
  if (cableData.value.length > 1) {
    proxy.$message.warning("添加失败，只能添加二条数据！");
    return;
  }
  cableData.value.push({
    xlName: `线路${cableData.value.length + 1}`,
    dlxh: cableTypeList.value[0].moduleid,
    status: "New",
    kxName: `馈线名称${cableData.value.length + 1}`,
    zdt: true,
    id: cableData.value.length,
  });
};
const deleteRow = (index) => {
  if (cableData.value.length == 1) {
    proxy.$message.warning("删除失败，至少保留一条电缆！");
    return;
  }
  cableData.value.splice(index, 1);
};
const channelTypeList = ref([]);
const handleChannelClass = (e) => {
  getChannelValue({ moduleTypeKey: e }).then((res) => {
    channelTypeList.value = res.data;
    cableForm.value.channelType = res.data[0].moduleid;
  });
};

// !绘制电缆 ----end

//!插入电缆头 ----start
const cableHeadData = ref([
  {
    zyFlag: false, //自由插入开关
    dfFlag: false, //等分插入开关
    djFlag: false, //等距插入开关
    dltNumFlag: false, //电缆头数量开关
    dltDistanceFlag: false, //电缆头间距开关
  },
]);
//中间头 list
const zjtList = ref([]);
//终端头 list
const zdtList = ref([]);
const dltLxChange = (e) => {
  if (e == 1) {
    cableHeadData.value.zyFlag = false;
    cableHeadData.value.dfFlag = false;
    cableHeadData.value.djFlag = false;
    cableHeadData.value.dltNumFlag = false;
    cableHeadData.value.dltDistanceFlag = false;
  } else {
    cableHeadData.value.zyFlag = true;
    cableHeadData.value.dfFlag = true;
    cableHeadData.value.djFlag = true;
    cableHeadData.value.dltNumFlag = true;
    cableHeadData.value.dltDistanceFlag = true;
  }
};
const handleInsertType = (e) => {
  if (e == 2) {
    cableForm1.value.cableHeadDistance = "";
    cableHeadData.value.dltDistanceFlag = true;
    cableHeadData.value.dltNumFlag = false;
  } else if (e == 3) {
    cableForm1.value.cableHeadNum = "";
    cableHeadData.value.dltNumFlag = true;
    cableHeadData.value.dltDistanceFlag = false;
  } else {
    cableHeadData.value.dltNumFlag = true;
    cableHeadData.value.dltDistanceFlag = true;
    cableForm1.value.cableHeadDistance = "";
    cableForm1.value.cableHeadNum = "";
  }
};
const selectRef = ref(null);
const changeRef = () => {
  const selectDropdown = document.querySelectorAll(".el-select-dropdown");
  const select1Width = selectRef.value.$el.offsetWidth;
  selectDropdown[1].style.maxWidth = `${select1Width}px`;
};

// 绘制电缆头
const dltClick = () => {
  let index = zjtList.value.findIndex(
    (item) => item.moduleid === cableForm1.value.cable1
  );
  if (cableForm1.value.checkRadio == "1" && cableForm1.value.dltLx == "1") {
    const options = { symbolId: zjtList.value[index].symbolId };
    sendMessage(
      cadAppRef.value,
      { type: "greeting", content: "SDK_extend_cable_dlt", options },
      (res) => {
        let arr = res.result.data;
        if (zjtList.value[index].modulename == "自动匹配") {
          let sdkCableName = res.result.data.find((item) => {
            return item.CLASS_NAME === "PWCableSecPSR" && item.LENGTH;
          });
          selectCableMiddleHead({ equipmentId: sdkCableName.DEV_ID }).then(
            (res1) => {
              cableForm1.value.cable1 = res1.data;
              cableHead(arr, cableForm1.value, zjtList.value, index);
              closeDialog();
            }
          );
        } else {
          cableHead(arr, cableForm1.value, zjtList.value, index);
          closeDialog();
        }
      }
    );
  } else if (
    cableForm1.value.checkRadio == "2" &&
    cableForm1.value.dltLx == "1"
  ) {
    if (cableForm1.value.cableHeadNum == "") {
      proxy.$modal.msgWarning("请填写电缆头数量");
    } else {
      if (appStore.sdkClassName.clickItemClassName != "PWCableSecPSR") {
        proxy.$modal.msgWarning("请选择电缆段！");
        return;
      }
      const options = { num: parseInt(cableForm1.value.cableHeadNum) };
      sendMessage(
        cadAppRef.value,
        { type: "greeting", content: "SDK_equally_connector", options },
        (res) => {
          let arr = res.params.result.data;
          if (zjtList.value[index].modulename == "自动匹配") {
            selectCableMiddleHead({
              equipmentId: appStore.sdkClassName.clickItem,
            }).then((res1) => {
              cableForm1.value.cable1 = res1.data;
              cableHead(arr, cableForm1.value, zjtList.value, index);
              closeDialog();
            });
          } else {
            cableHead(arr, cableForm1.value, zjtList.value, index);
            closeDialog();
          }
        }
      );
    }
  } else if (
    cableForm1.value.checkRadio == "3" &&
    cableForm1.value.dltLx == "1"
  ) {
    if (cableForm1.value.cableHeadDistance == "") {
      proxy.$modal.msgWarning("请填写电缆头间距");
    } else {
      if (appStore.sdkClassName.clickItemClassName != "PWCableSecPSR") {
        proxy.$modal.msgWarning("请选择电缆段！");
        return;
      }
      const options = { length: cableForm1.value.cableHeadDistance };
      sendMessage(
        cadAppRef.value,
        { type: "greeting", content: "SDK_equidistant_connector", options },
        (res) => {
          let arr = res.params.result.data;
          if (zjtList.value[index].modulename == "自动匹配") {
            selectCableMiddleHead({
              equipmentId: appStore.sdkClassName.clickItem,
            }).then((res1) => {
              cableForm1.value.cable1 = res1.data;
              cableHead(arr, cableForm1.value, zjtList.value, index);
              closeDialog();
            });
          } else {
            cableHead(arr, cableForm1.value, zjtList.value, index);
            closeDialog();
          }
        }
      );
    }
  }
};
//!插入电缆头  ----end

//!绘制电缆分支箱   --start
const lineIntervalFlag = ref(true);
//电缆分支箱list
const dlfzxLbList = ref([]);
//电缆分支箱list
const dlfzxList = ref([]);
//电压等级
const voltageLevelChange = (e) => {
  // 电缆分支箱类别取 ModuleTypeKey ModuleTypeName
  //  电缆分支箱 ModuleID ModuleName
  if (cableForm2.value.status !== "New") {
    lineIntervalFlag.value = false;
  } else {
    lineIntervalFlag.value = true;
  }
  getModuleTypeKeyList({ vol: e }).then((res) => {
    dlfzxLbList.value = res.data;
    cableForm2.value.cableBoxType = res.data[0].moduletypekey;
    cableBoxLbChange(res.data[0].moduletypekey);
  });
};
//电缆分支箱类别
const cableBoxLbChange = (e) => {
  getCableBranchBoxList({
    moduleTypeKey: e,
    voltageLevel: cableForm2.value.voltageLevel,
  }).then((res) => {
    dlfzxList.value = res.data;
    cableForm2.value.cableBox = res.data[0].moduleid;
    cableBoxModuleChange(res.data[0].moduleid);
  });
};
//电缆分支箱模块
const cableBoxModuleChange = (e) => {
  getCableBoxInOutData({ moduleID: e }).then((res) => {
    cableForm2.value.lineInIntervals = new Array(res.data?.InNum || 0).fill("");
    cableForm2.value.lineOutIntervals = new Array(res.data?.OutNum || 0).fill(
      ""
    );
  });
};

//绘制电缆分支箱
const drawCableBox = () => {
  const { sdkClassName, symbolId, legendtypekey } = dlfzxLbList.value.find(
    (o) => o.moduletypekey === cableForm2.value.cableBoxType
  );
  const options = {
    symbolId, // 站房图元ID
    symbolClassName: (sdkClassName || "").split("-")[0], // 站房类型
    stationName: cableForm2.value.deviceCode, // 站房名称
    aliasname: cableForm2.value.deviceCode, // 站房简称
    voltageLevel: cableForm2.value.voltageLevelOption.find(
      (o) => o.text === cableForm2.value.voltageLevel
    ).value, // 电压等级代码
    info: [
      {
        // 站房内相关信息，一个对象代表一段母线
        bayInfo: [
          // 间隔信息，一个对象代表一个间隔   间隔类型，间隔名称，间隔编号
        ],
        mlInfo: {
          NAME: cableForm2.value.deviceCode + "Ⅰ段母线", // 母线名称
          ALIAS_NAME: "Ⅰ段母线", // 母线简称
        },
      },
    ],
  };
  for (let o = 0; o < cableForm2.value.lineInIntervals.length; o++) {
    options.info[0].bayInfo.push({
      bayType: "6",
      bayName: "进线柜",
      bayNo: cableForm2.value.lineInIntervals[o],
    });
  }
  for (let o = 0; o < cableForm2.value.lineOutIntervals.length; o++) {
    options.info[0].bayInfo.push({
      bayType: "1",
      bayName: "出线柜",
      bayNo: cableForm2.value.lineOutIntervals[o],
    });
  }
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_dlfzx", options },
    (res) => {
      console.log("SDK_dlfzx回调", res);
      if (res.cmd === "SDK_dlfzx") {
        drawEndCableBox(
          res.params.status === "000000" && res.params.result,
          cableForm2.value,
          { sdkClassName, legendtypekey }
        );
      }
    }
  );
};
//!绘制电缆分支箱   --end

//!绘制电缆井   --start
const dlList = ref({
  lxList: [],
  dljxhList: [],
  tzlxList: [],
});
const changeType = (e) => {
  getCableWellTypeNumber({ moduleTypeKey: e }).then((res) => {
    dlList.value.dljxhList = res.data;
    cableForm4.value.cableModel =
      res.data.length === 0 ? "" : res.data[0].moduleid;
  });
};
const drawWell = () => {
  if (!cableForm4.value.cableModel) {
    proxy.$modal.msgWarning("未选择工井型号！");
    return;
  }
  const { sdkclassName, symbolId, legendtypekey } = dlList.value.dljxhList.find(
    (o) => o.moduleid === cableForm4.value.cableModel
  );
  const options = {
    cableChanelInfo: {
      name: cableForm4.value.lineName, // 通道名称
      id: "",
    },
    direction: "1",
    wellStartNo: cableForm4.value.userNumber, // 井起始编号
    prefix: "", // 井编号前缀
    suffix: "", // 井编号后缀
    symbolId: symbolId, // 井图元ID
    isGenerateCableJoint: false, // 是否自动生成中间接头
    selectedCableSecIds: [], // 电缆段集合
  };
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_well", options },
    (res) => {
      console.log("SDK_well回调", res);
      if (res.cmd === "SDK_well") {
        drawEndWell(
          res.params.status === "000000" && res.params.result,
          cableForm4.value,
          { sdkclassName, legendtypekey }
        );
      }
    }
  );
};
//!绘制电缆井   --end

//!绘制电缆通道  ---start
const cableNumFlag = ref(true);
//通讯管选择-1 电缆井参数-2 默认-0
const channelTube = ref("0");
//电缆井编号规则
const cableRules = ref(false);
const hzdlList = ref({
  tzlxList: [],
  gcList: [],
  tdlxList: [],
  gclxList: [],
  gcxhList: [],
  gclxList1: [],
  gcxhList1: [],
});
const checkRadioChange = (e) => {
  if (e) {
    cableNumFlag.value = false;
  } else {
    cableNumFlag.value = true;
  }
};

const channelChange = (e) => {
  if ((e.length == 1 && e[0] == "自动匹配电缆井") || e.length > 1) {
    cableRules.value = false;
  } else {
    cableRules.value = true;
  }
};

const handleLayMethod = (e) => {
  if (e == "PG") {
    channelTube.value = "0";
  } else if (e == "FKWLG") {
    channelTube.value = "1";
  } else {
    channelTube.value = "2";
  }
  getModulesByModuleTypeKey({ moduleTypeKey: e }).then((res) => {
    hzdlList.value.tdlxList = res.data;
    cableForm3.value.channelModel = res.data[0].moduleid;
  });
};

const handlePipeType1 = (e) => {
  pipeModels({ value: e }).then((res) => {
    let resarr = res.data.filter((item) => item.moduletypekey == "DLBHG");
    hzdlList.value.gcxhList = resarr;
    cableForm3.value.pipeModel1 = resarr[0].moduleid;
  });
};
const handlePipeType2 = (e) => {
  pipeModels({ value: e }).then((res) => {
    let resarr = res.data.filter((item) => item.moduletypekey == "DLTXG");
    hzdlList.value.gcxhList1 = resarr;
    cableForm3.value.pipeModel2 = resarr[0].moduleid;
  });
};
//!绘制电缆通道  ---end
const data = ref([
  {
    name: "线路1",
    key: "New",
    sk: " JG/TH",
    ksd: "删除",
  },
]);

const closeDialog = () => {
  appStore.mapIndex = "";
};

const towerSelectVisible = ref(false);

// 绘制电缆路径
// const drawCableLineChange = () => {
//   console.log("cableForm.value.checkBox", cableForm.value.checkBox);
//   const isExtend = cableForm.value.checkBox[0];
//   if (isExtend === "1") {
//     console.log("触发1");
//     extendCableLine();
//   } else if (isExtend === "3") {
//     addSoilLine();
//   } else {
//     console.log("触发2");
//     addCableLine();
//   }
// };
const drawCableLineChange = () => {
  console.log("cableForm.value.checkBox", cableForm.value.checkBox);
  if (cableForm.value.checkBox == "1") {
    //绘制电缆
    addCableLine();
  } else if (cableForm.value.checkBox == "2") {
    //继续绘制
    extendCableLine();
  } else if (cableForm.value.checkBox == "3") {
    //基于土建路径
    addSoilLine();
  } else {
    //管道增线
    addPiping();
  }
};
//管道增线
const addPiping = () => {
  const cableFirst = cableTypeList.value.find(
    (o) => o.moduleid === cableData.value[0].dlxh
  );
  const { legendTypeKey, voltage } = cableFirst;
  const channel = channelTypeList.value.find(
    (o) => o.moduleid === cableForm.value.channelType
  );
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_add_piping", options: {} },
    (res) => {
      console.log("SDK_add_piping", res);
      if (res.cmd === "SDK_add_piping") {
        drawEndCable(
          res.params.status === "000000" && res.params.result,
          cableData.value,
          {
            legendTypeKey,
            voltage,
            LayingName: channel.modulecode,
            LayingFullName: channel.modulename,
            LayingModuletypekey: channel.moduletypekey,
            Laying: channel.moduleid,
            ...cableForm.value,
          }
        );
      }
    }
  );
};
const cableLineParams = reactive({
  startTower: "",
  endTower: "",
  sdkParams: null,
});

const towerDataType = ref("start");

const updateTowerParams = (val) => {
  if (towerDataType.value === "start") {
    cableLineParams.startTower = val.moduleid;
  }
  if (towerDataType.value === "end") {
    cableLineParams.endTower = val.moduleid;
    addCableLineSubmit();
  }
};

const addCableLineSubmit = () => {
  console.log("cableLineParams", cableLineParams);
  /* const form = cableForm.value
  // const AssociatedParent = [e.blockId]
  const params = {
    moduleId: form.deviceModel, // 顶层id
    legendTypeKey: paramsListBox.legendtypekey, // 图元类型
    legendState: form.status, // 状态
    legendGuidKey: generateUuid(), // 设备id
    engineeringId: taskId,
    privatepropertys: JSON.stringify({
      UserNumber: form.deviceCode, // 编号
      Has_IntelligentSwitch: form.isFlag, // 是否智能化
    }),
    topologyrelations: JSON.stringify({
      AssociatedIn: [], // 变压器不涉及
      AssociatedOut: [], // 变压器不涉及
      AssociatedParent: [], // 绘制父设备的DEV_ID
      AssociatedChild: [], // 后端处理
      AssociatedLabel: [], // 目前不涉及
      AssociatedLabelOwner: [], // 目前不涉及
      AssociatedFile: [], // 目前不涉及
    }),
  };
  saveAllInfos(params).then((res) => {
    if (res.code === 200) {
      proxy.$message.success("保存成功");
    } else {
      proxy.$message.error(res.msg);
    }
  }); */
};

const cadAppRef = inject("cadAppRef");

const addCableLine = () => {
  towerDataType.value = "start";
  console.log("电缆页面", cableData.value, cableForm.value);
  const cableFirst = cableTypeList.value.find(
    (o) => o.moduleid === cableData.value[0].dlxh
  );
  const { legendTypeKey, voltage } = cableFirst;
  const channel = channelTypeList.value.find(
    (o) => o.moduleid === cableForm.value.channelType
  );
  const voltageLevel = voltageLevelData.find((o) => o.text === voltage)?.value;
  // 基础信息
  const lines = cableData.value.map((item) => {
    return {
      lineId: "",
      lineName: item.xlName,
    };
  });
  let baseInfo = {
    // 电压等级（6kV|10kV|20kV|35kV）
    voltageLevel,
    // 起始设备ID
    startDeviceId: "",
    // 线路信息
    lines,
    symbolId: "", //角钢塔（直线型）:252016; 直线铁杆:252012; 直线钢管塔:252009;252011
  };
  // 设备信息
  let deviceInfo = {
    Substation: [],
    PWPolePSR: [],
  };
  let options = {
    baseInfo: baseInfo,
    deviceInfo: deviceInfo,
  };
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_add_cable_line", options },
    (res) => {
      console.log("SDK_add_cable_line回调", res);
      if (res.cmd === "SDK_add_cable_line") {
        drawEndCable(
          res.params.status === "000000" && res.params.result,
          cableData.value,
          {
            legendTypeKey,
            voltage,
            LayingName: channel.modulecode,
            LayingFullName: channel.modulename,
            LayingModuletypekey: channel.moduletypekey,
            Laying: channel.moduleid,
            ...cableForm.value,
          }
        );
      }
    }
  );
};
//基于土建路径
const addSoilLine = () => {
  let options = { name: cableData.value[0].xlName };
  const cableFirst = cableTypeList.value.find(
    (o) => o.moduleid === cableData.value[0].dlxh
  );
  const { legendTypeKey, voltage } = cableFirst;
  const channel = channelTypeList.value.find(
    (o) => o.moduleid === cableForm.value.channelType
  );
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_add_soil_line", options },
    (res) => {
      console.log("SDK_add_soil_line", res);
      if (res.params.status === "000000") {
        cableSoil(
          res.params.result.data,
          {
            legendTypeKey,
            voltage,
            LayingName: channel.modulecode,
            LayingFullName: channel.modulename,
            LayingModuletypekey: channel.moduletypekey,
            Laying: channel.moduleid,
            ...cableForm.value,
          },
          cableData.value
        );
      }
    }
  );
};
// 延长电缆线路
const extendCableLine = () => {
  const cableFirst = cableTypeList.value.find(
    (o) => o.moduleid === cableData.value[0].dlxh
  );
  const { legendTypeKey, voltage } = cableFirst;
  const channel = channelTypeList.value.find(
    (o) => o.moduleid === cableForm.value.channelType
  );
  // 基础信息
  let baseInfo = {
    // 电压等级（6kV|10kV|20kV|35kV）
    voltageLevel: "20",
    symbolId: "", //角钢塔（直线型）:252016; 直线铁杆:252012; 直线钢管塔:252009;252011
  };
  // 设备信息
  let deviceInfo = {
    Substation: [],
    PWPolePSR: [],
  };
  let options = {
    baseInfo: baseInfo,
    deviceInfo: deviceInfo,
  };
  sendMessage(
    cadAppRef.value,
    {
      type: "greeting",
      content: "SDK_extend_cable_line",
      options,
    },
    (res) => {
      if (res.cmd === "SDK_extend_cable_line") {
        drawEndCable(
          res.params.status === "000000" && res.params.result,
          cableData.value,
          {
            legendTypeKey,
            voltage,
            LayingName: channel.modulecode,
            LayingFullName: channel.modulename,
            LayingModuletypekey: channel.moduletypekey,
            Laying: channel.moduleid,
            ...cableForm.value,
          }
        );
      }
    }
  );
};

// 读取线路名称接口
const getEngineerLineFun = (lineType) => {
  return getEngineerLine({ engineeringObjid: taskId, type: lineType }).then(
    (res) => {
      return res.data.map((item) => ({
        label: item.Name,
        value: item.Name,
      }));
    }
  );
};

//绘制电缆通道
const drawDltd = () => {
  // cableForm3.value.layMethod//敷设方式
  // hzdlList.value.gcList
  let arr = hzdlList.value.gcList.find(
    (o) => o.moduletypekey === cableForm3.value.layMethod
  );
  if (arr.moduletypekey === "FKWLG") {
    arr.symbolId = "500311";
    arr.sdkClassName = "CableChannelSec-TY_PG";
  }
  if (
    cableForm3.value.cableCode2 == "" ||
    cableForm3.value.cableCode1 == "" ||
    cableForm3.value.cableCode3 == ""
  ) {
    proxy.$modal.msgWarning("请补全电缆井编号规则");
  } else {
    let options = {
      cableChanelInfo: {
        name: cableForm3.value.lineName,
        id: "",
      },
      direction: "1",
      wellStartNo: cableForm3.value.cableCode2,
      prefix: cableForm3.value.cableCode1,
      suffix: cableForm3.value.cableCode3,
      symbolId: arr.symbolId,
    };
    if (channelTube.value == 2) {
      cableForm3.value.pipeModel1 = "";
      cableForm3.value.pipeModel2 = "";
    } else if (channelTube.value == 1) {
      cableForm3.value.pipeModel2 = "";
    }
    sendMessage(
      cadAppRef.value,
      {
        type: "greeting",
        content: "SDK_dltd",
        options,
      },
      (res) => {
        console.log(res, "绘制电缆通道子页面回调");
        drawCableChannel(
          res.params.result.data,
          cableForm3.value,
          arr,
          res.unipment
        );
      }
    );
  }
};

const lineOption = ref({
  tableNameOption: [],
});

// 通道内敷设位置图
const cableList = ref([]);
const cableType = ref([]);
const cableNum = ref([]);
//打开剖面
const openProfile = () => {
  sendMessage(
    cadAppRef.value,
    {
      type: "greeting",
      content: "SDK_openProfile",
      options: {},
    },
    (res) => {
      console.log(res, "打开剖面");
    }
  );
};
//保存
const saveProfile = async () => {
  try {
    const res = await new Promise((resolve, reject) => {
      sendMessage(
        cadAppRef.value,
        {
          type: "greeting",
          content: "SDK_proFile",
          options: {},
        },
        (response) => {
          resolve(response);
        }
      );
    });

    if (res.params.message === "剖面编辑数据保存失败") {
      proxy.$message.warning(res.params.result);
      return;
    }

    if (res.params.status === "000000") {
      const obj = res.params.result;

      //取通道类型
      const data1 = {
        legendguidkeys: [obj.CableChannelSec[0].DEV_ID],
        taskid: taskId,
      };
      getPropertyData(data1).then((response) => {
        cableType.value = response.data[0].LegendAttributes.filter(
          (item) => item.PropertyName == "名称"
        );
      });
      cableNum.value = [
        ...new Map(
          obj.CablePointGeom.map((item) => [item.CABLE_SEC_ID, item])
        ).values(),
      ];
      const parentId = obj.InfSGPipeSectionGeom[0].DEV_ID;
      const condList = obj.LayUnit.filter(
        (item) => item.PARENT_ID === parentId
      ).map((item) => {
        const cablePoint = obj.CablePointGeom.find(
          (cp) => cp.PARENT_ID === item.DEV_ID
        );
        return {
          ...item,
          dlId: cablePoint?.CABLE_SEC_ID,
          NAME: cablePoint?.NAME,
          HOLE_COLUMN: obj.InfSGPipeSectionGeom[0].HOLE_COLUMN,
          HOLE_ROW: obj.InfSGPipeSectionGeom[0].HOLE_ROW,
        };
      });

      const arrId = condList
        .filter((item) => item.dlId)
        .map((item) => item.dlId);

      const data = {
        legendguidkeys: arrId,
        taskid: taskId,
      };

      if (arrId.length === 0) {
        return;
      }

      try {
        const res1 = await getPropertyData(data);
        const propertyMap = new Map();
        res1.data[0].LegendAttributes.forEach((attr) => {
          if (attr.PropertyName === "名称") {
            propertyMap.set(1, attr.AttributeValue);
          } else if (attr.PropertyName === "状态") {
            propertyMap.set(2, attr.AttributeValue);
          }
        });
        condList.forEach((item) => {
          if (item.dlId) {
            item.xh = propertyMap.get(1);
            item.state = propertyMap.get(2);
          }
        });
        cableList.value = condList.sort(
          (a, b) => parseInt(a.NO) - parseInt(b.NO)
        );
      } catch (error) {
        console.error("Error fetching property data:", error);
      }
    }
  } catch (error) {
    console.error("保存剖面数据失败:", error);
    proxy.$message.error("保存剖面数据失败");
  }
};

const generateDraw = () => {
  oniframeMessage({
    type: "cadPreview",
    content: "Mx_tdnfwzt",
    formData: {
      tableData: JSON.stringify(cableList.value),
      countyOrganisationName: route.query.countyOrganisationName,
      projectName: route.query.projectName,
      stage: route.query.stage,
      proCode: route.query.compilingOrganisationName,
    },
  });
};
const oniframeMessage = (param) => {
  const myiframe = appStore.iframe;
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener("message", handleMessage);
    // 添加新的事件监听器
    window.addEventListener("message", handleMessage);
  }
};
const handleMessage = async (event) => {
  console.log("🚀 ~ handleMessage ~ event:", event);

  // 1. 验证消息结构
  if (!isValidCadMessage(event)) return;

  try {
    let obj = event.data.params.formData;
    const formData = new FormData();
    formData.append("taskId", taskId);
    const newFileName = `通道内敷设位置图.dwg`;
    formData.append("files", obj.files[0], newFileName);
    intervalAddFile(formData).then((res) => {
      if (res?.code === 200) {
        proxy.$message.success("保存成功");
        callChildC?.();
      }
    });
  } catch (error) {
    console.error("文件处理失败:", error);
  }
};
function isValidCadMessage(event) {
  return (
    event.data?.type === "parentCad" &&
    event.data?.params?.content === "通道内敷设位置图"
  );
}
onMounted(() => {});
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "绘制电缆路径") {
      cableForm.value.channelClass = "PG";
      cableForm.value.channelStatus = "New";
      cableForm.value.cableDistance = "";
      cableForm.value.prefix = "";
      cableForm.value.suffix = "";
      cableForm.value.content = "";
      //初始化
      flag.value = {
        tdLb: false, //通道类型开关
        tdType: false, //通道类型开关
        tdStatus: false, //通道状态开关
        jjFlag: false, //电缆绘制间距开关
        qzFlag: true, //前缀开关
        hzFlag: true, //后缀开关
        contentFlag: true, //前缀 后缀中间内容开关
        zdtFlag: true, //终端头开关
        delFlag: false, //删除开关
        jxFlag: false, //继续绘制
        zgFlag: false, //主干绘制
        jytjFlag: false, //基于土建路径
        hzdljFlag: false, //绘制电缆井
        addFlag: false, //增加开关
      };
      cableForm.value.checkBox = "1";
      handleChannelClass(cableForm.value.channelClass);
      getCableTypeFull({ cableState: "", taskId: taskId }).then((res) => {
        cableTypeList.value = res.data;
        cableData.value = [
          {
            xlName: "线路1",
            dlxh: cableTypeList.value[0].moduleid,
            status: "New",
            kxName: "馈线名称1",
            zdt: true,
          },
        ];
      });
      getEngineerLineFun("架空").then((options) => {
        lineOption.value.tableNameOption = options; // 手动赋值
      });
    } else if (newInfo == "插入电缆头") {
      getCableHeadType({ cableTypeName: "中间头" }).then((res) => {
        res.data.unshift({
          modulename: "自动匹配",
          moduleid: "1",
          symbolId: "250501",
          legendTypeKey: "TY_DLZJT",
          voltage: "",
        });
        zjtList.value = res.data;
        cableForm1.value.cable1 = res.data[0].moduleid;
      });
      getCableHeadType({ cableTypeName: "终端头" }).then((res) => {
        res.data.unshift({
          modulename: "自动匹配",
          moduleid: "2",
          symbolId: "250501",
          legendTypeKey: "TY_DLZDT",
          voltage: "",
        });
        zdtList.value = res.data;
        cableForm1.value.cable2 = res.data[0].moduleid;
      });
      cableHeadData.value.dltNumFlag = true;
      cableHeadData.value.dltDistanceFlag = true;
      cableForm1.value.cableHeadDistance = "";
      cableForm1.value.cableHeadNum = "";
      cableForm1.value.checkRadio = "1";
    } else if (newInfo == "绘制电缆分支箱") {
      cableForm2.value.voltageLevel = "10kV";
      cableForm2.value.status = "New";
      cableForm2.value.deviceCode = "";
      cableForm2.value.lineName = "";
      voltageLevelChange(cableForm2.value.voltageLevel);
    } else if (newInfo == "绘制电缆井") {
      cableForm4.value.lineName = "线路名称1";
      cableForm4.value.status = "New";
      cableForm4.value.userNumber = "1";
      cableForm4.value.radioMethod = "1";
      cableForm4.value.brokenRoadArea = "";
      getCableWellType().then((res) => {
        dlList.value.lxList = res.data;
        cableForm4.value.type = res.data[0].moduletypekey;
        changeType(res.data[0].moduletypekey);
      });
      getDicts({ dictionarKeys: ["Geological"] }).then((res) => {
        if (res.code === 200 && res.data) {
          dlList.value.tzlxList = res.data.Geological;
          cableForm4.value.soil = res.data.Geological[0].key;
        }
      });
      cableForm4.value.brokenRoad = "混凝土路面";
    } else if (newInfo == "绘制电缆通道") {
      cableForm3.value.cableCode1 = "";
      cableForm3.value.cableCode2 = "";
      cableForm3.value.cableCode3 = "";
      cableForm3.value.brokenRoad = "混凝土路面";
      cableForm3.value.lineName = "线路名称1";
      cableForm3.value.status = "New";
      cableNumFlag.value = true;
      channelTube.value = "0";
      getDicts({ dictionarKeys: ["Geological"] }).then((res) => {
        if (res.code === 200 && res.data) {
          hzdlList.value.tzlxList = res.data.Geological;
          cableForm3.value.soil = res.data.Geological[0].key;
        }
      });
      moduleTypes().then((res) => {
        hzdlList.value.gcList = res.data;
        cableForm3.value.layMethod = res.data[0].moduletypekey;
        handleLayMethod(res.data[0].moduletypekey);
      });
      pipeTypes().then((res) => {
        hzdlList.value.gclxList = res.data;
        cableForm3.value.pipeType1 = res.data[0];
        cableForm3.value.pipeType2 = res.data[0];
        handlePipeType1(res.data[0]);
        handlePipeType2(res.data[0]);
      });
    } else if (newInfo == "通道内敷设位置图") {
      cableList.value = [];
      cableType.value = [];
      cableNum.value = [];
    }
  }
);
</script>

<style scoped lang="scss">
::v-deep .el-select {
  // width: 257px;
  width: 90%;
  // height: 27px;
}

::v-deep .el-popper .el-select-dropdown {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 259px;
}

::v-deep .el-select__wrapper {
  min-height: 27px;
}

::v-deep .el-form-item.el-form-item--default {
  margin-bottom: 0;
  line-height: 0;
}

::v-deep .el-form-item--default .el-form-item__content {
  line-height: normal;
}

::v-deep .el-form-item__error {
  right: 22px !important;
  top: 12px !important;
  left: auto;
}

::v-deep .el-select__placeholder {
  color: #797979;
  font-weight: 400;
}

::v-deep .el-input__inner {
  color: #282b33;
  font-weight: 400;
}

h4 {
  font-family: "Noto Sans SC", sans-serif;
}

.title-text {
  color: #0e8b8d;
  font-size: 16px;
  font-weight: bold;
  font-family: "Noto Sans SC", sans-serif;
  margin-bottom: 2px;
}

.title-color {
  width: 4px;
  height: 15px;
  background: #0e8b8d;
  margin: 0 10px;
}

.check_radio {
  ::v-deep .el-checkbox__label {
    color: #282b33;
    font-weight: 400;
  }
}

.line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  width: 100%;

  :last-child {
    margin-bottom: 0;
  }

  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }

  .tw-ipt {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    // span {
    //   width: 60px;
    // }
  }

  .radio-group {
    color: #282b33;
    font-weight: 400;

    ::v-deep .el-radio__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .checkbox-group {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }

  .input-table {
    margin-top: -2px;
    margin-bottom: 1px;
  }

  .tw-sct {
    width: 105px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    .in-item {
      width: 82px;
      height: 27px;
    }
  }

  .line-item {
    width: 104px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }

  .line-input {
    width: calc(100% - 104px);
    // flex: 1;
    //   width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    .in-item {
      // width: 257px;
      width: 90%;
      height: 27px;
    }
  }

  .line-no-display {
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }

  .radio-input {
    // width: 294px;
    width: calc(100% - 104px);
    height: 60px;
    display: flex;
    background: #e4f2f2;
    flex-direction: column;

    .in-item {
      height: 20px;

      ::v-deep .el-input__wrapper {
        width: 150px;
      }
    }
  }

  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}

.line-prefix {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;

  .line-item {
    width: 104px;
    height: 120px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }

  .line-input {
    // flex: 1;
    width: calc(100% - 104px);
    //   width: 294px;
    height: 120px;
    background: #e4f2f2;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .in-item {
      // width: 100px;
      width: 90%;
      height: 27px;
      margin-top: 5px;
      margin-left: 5%;
    }
  }
}

// 标注样式
.bz-line {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1px;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  height: 150px;

  .bz-left {
    width: 100px;
    margin-left: 10px;

    .bz-border {
      padding: 0 10px;
      border: #69b5b6 2px solid;
      height: 120px;
    }
  }

  .bz-right {
    width: 500px;
    margin-left: 10px;

    .input-table {
      margin-top: -2px;
      margin-bottom: 1px;
    }

    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

.material {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }

  .active {
    background: #0e8b8d !important;
    color: white !important;
  }
}

.small-material {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 11px !important;
    color: #ffffff;
    font-weight: 600;
  }

  ::v-deep .el-table td .el-table__cell div {
    font-size: 10px !important;
    font-weight: 600;
  }
}

.meter-draw {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }

  .draw {
    font-size: 12px;
    color: #282b33;
    font-weight: bold;

    span {
      margin-left: 10px;
    }
  }
}

.draw-footer {
  margin: 0 10px 10px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid #badddd;
}

.photo-box {
  padding: 10px;
  display: flex;
  height: 450px;
  justify-content: flex-start;
  background: #e4f2f2;

  .photo-left {
    width: 200px;
    height: 430px;

    .left-tree {
      border: 2px solid #badddd;
      padding: 10px;
      height: 400px;

      .el-tree {
        background: transparent;
      }

      ::v-deep .el-tree-node:focus > .el-tree-node__content {
        background: rgba(80, 169, 170, 0.4);
      }

      ::v-deep .el-tree-node :hover {
        background: rgba(80, 169, 170, 0.1) !important;
      }
    }
  }

  .photo-right {
    margin-left: 10px;
    width: 600px;
    height: 430px;

    .photo-border {
      height: 30px;
      width: 600px;
      border: 2px solid #badddd;
    }

    .photo-table {
      width: 350px;

      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }

    .photo-img {
      margin-top: -2px;
      width: 250px;
      background: white;
    }
  }
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background: #fff;
}

/* 修改Element UI中el-checkbox选中时的对号颜色 */
::v-deep .el-checkbox__inner:after {
  border-color: #07888a; /* 将对号颜色改为红色 */
}
</style>
