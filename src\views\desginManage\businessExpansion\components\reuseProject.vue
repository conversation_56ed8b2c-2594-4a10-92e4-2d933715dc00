<template>
  <el-dialog
    title="图模复用"
    v-model="props.modelValue"
    width="1000px"
    append-to-body
    @close="close"
  >
    <div>
        <el-table
          class="demo-table"
          stripe
          :data="props.data"
          row-key="menuId"
        >
          <el-table-column
            prop="projectName"
            label="项目名称"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="projectWholeprocessId"
            label="可研项目编码"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="cityOrganisationName"
            label="所属市公司"
            width="120"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="countyOrganisationName"
            label="所属县公司"
            width="120"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            prop="voltageLevel"
            label="电压等级"
            width="80"
          ></el-table-column>
          <el-table-column prop="createTime" label="任务下达时间" width="110">
            <template #default="scope">
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.createTime"
                placement="top"
              >
                <span>{{ formatDate(scope.row.createTime) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="state" label="任务状态" width="80">
            <template #default="scope">
              <span v-if="scope.row.state === '1'">进行中</span>
              <span v-else-if="scope.row.state === '2'">已完成</span>
              <span  v-else-if="scope.row.state === '0'">未开始</span>
              <span v-else-if="scope.row.state === '3'">移交中</span>
              <span  v-else-if="scope.row.state === '4'">已退回</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            :width="100"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <div>
                <el-button
                  color="#1abc9c"
                  style="color: #fff"
                  round
                  @click="startOnlineDesgin(scope.row)"
                  >复用
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
    </div>
    <!-- <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormDialog">确 定</el-button>
      </div>
    </template> -->
  </el-dialog>
</template>

<script setup>

import { ElMessageBox } from "element-plus";

const taskList = ref([]);

const props = defineProps({
  modelValue: {
    // v-model 默认绑定到 modelValue
    type: Boolean,
    default: false,
  },
  getEngineeringList: {
    type: Object,
    required: true,
  },
  data:{
    type:Array,
    required:true
  }
});
const emit = defineEmits(["submitFormDialog", "cancelDialog",'subStart']);

const startOnlineDesgin=(item)=>{
  emit('subStart',item)
}
const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const cancelDialog = () => {
  emit("cancelDialog");
};

const close = () => {
  emit("close");
};





// 在组件挂载时获取数据
onMounted(() => {
    console.log("🚀 ~ onMounted ~ props.data:", props.data)
});
</script>

<style scoped lang="scss">
/* 你的样式 */
.elCollapse {
  ::v-deep .el-collapse-item__arrow {
    margin-left: 8px;
    margin-bottom: 1px;
  }
}
</style>
