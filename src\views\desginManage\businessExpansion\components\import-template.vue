<script setup>
import {getTemplateDataByType} from "@/api/desginManage/GccsDialog.js";
import {ElMessage} from "element-plus";

const visible = defineModel('visible', {type: Boolean, default: false});

const emit = defineEmits(['updateParams'])

const templateOption = ref([])
const getData = () => {
  const params = {
    type: 'UserEngineeringSetting'
  }
  getTemplateDataByType(params).then(res => {
    templateOption.value = res.data
  })
}

const formRef = ref()
const form = reactive({objId: ''})
const rules = reactive({
  objId: [
    {required: true, message: '请输入', trigger: 'change'},
  ]
})

const loading = ref(false)
const onSubmit = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (!valid) return
    const data = templateOption.value.find(item => item.objId === form.objId)?.templateData
    if (data) {
      emit('updateParams', data)
      ElMessage.success('导入成功！')
      visible.value = false
    } else {
      ElMessage.error('导入失败！')
    }
  })
}

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}

const closeVisible = () => {
  resetForm(formRef.value)
}
</script>

<template>
  <el-dialog
      title="导入模板"
      v-model="visible"
      width="30%"
      append-to-body
      destroy-on-close
      @open="getData"
      @close="closeVisible"
  >
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="名称" prop="objId">
        <el-select v-model="form.objId" placeholder="请选择">
          <el-option v-for="item in templateOption" :label="item.templateName" :value="item.objId"/>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onSubmit(formRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
