<template>
  <data-dialog
      @close="closeDialog"
      dataWidth="830px"
      v-if="appStore.mapIndex == '线夹匹配'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        线夹匹配
      </h4>
    </template>
    <template #body>
      <div class="photo-box">
        <div class="photo-right" style="width: 100%;">
          <el-tabs
              v-model="activeName"
              type="card"
              class="demo-tabs"
              @tab-click="handleClick"
          >
            <el-tab-pane label="耐张线夹" name="first" style="width: 100%;">
              <div style="background-color:#fff;border-radius: 5px;padding: 0 10px;color: #515a6e;font-size: 14px;display: flex;align-items: center">
                <el-checkbox v-model="checkedNz" style="margin-right: 10px;">
                  <template #default>
                    <span style="color: #515a6e">匹配时不改变线夹类别</span>
                  </template>
                </el-checkbox>
               <el-button size="small" @click="saveXjpp('nz')">保存</el-button>
               <el-button size="small">自动匹配线夹</el-button>
              </div>
              <el-table
                  :data="tableDataNz"
                  :span-method="objectSpanMethodNz"
                  border
                  height="360px"
                  style="width: 100%;"
              >
                <el-table-column prop="wirecliptype" label="线夹类别" width="150px"/>
                <el-table-column prop="materialsprojectid" label="规格型号">
                  <template #default="scope">
                    <el-select
                        v-model="scope.row.materialsprojectid"
                        placeholder=""
                        size="small"
                        style="width: 300px"
                    >
                      <el-option
                          v-for="item in scope.row.option"
                          :key="item.materialsprojectid"
                          :label="item.spec"
                          :value="item.materialsprojectid"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="suitablesection" label="适用导线截面" width="110px" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="并沟线夹" name="second">
              <div style="background-color:#fff;border-radius: 5px;padding: 0 10px;color: #515a6e;font-size: 14px;display: flex;align-items: center">
                <el-checkbox v-model="checkedNz" style="margin-right: 10px;">
                  <template #default>
                    <span style="color: #515a6e">匹配时不改变线夹类别</span>
                  </template>
                </el-checkbox>
                 <el-button size="small" @click="saveXjpp('bg')">保存</el-button>
                 <el-button size="small">自动匹配线夹</el-button>
                <!-- <div style="margin-left: 15px;cursor: pointer">保存</div>
                <div style="margin-left: 15px;cursor: pointer">自动匹配线夹</div> -->
              </div>
              <el-table
                  :data="tableDataBg"
                  :span-method="objectSpanMethodBg"
                  border
                  height="360px"
                  style="width: 100%;"
              >
                <el-table-column prop="wirecliptype" label="线夹类别" width="150px">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.checked" @change="handleCheckChange(scope.row)">
                      <template #default>
                        <span style="color: #515a6e">{{ scope.row.wirecliptype }}</span>
                      </template>
                    </el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="materialsprojectid" label="规格型号">
                  <template #default="scope">
                    <el-select
                        v-model="scope.row.materialsprojectid"
                        placeholder=""
                        size="small"
                        style="width: 200px"
                    >
                      <el-option
                          v-for="item in scope.row.option"
                          :key="item.materialsprojectid"
                          :label="item.spec"
                          :value="item.materialsprojectid"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="mainsection" label="主导线" width="110px" />
                <el-table-column prop="subsection" label="支导线" width="110px" />
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useRoute } from "vue-router";
import {ElMessage} from 'element-plus'
import {
  GetDataBingGou,
  AddDataBingGou,
  GetDataNaiZhang,
  getWireClipMaterialsProject,
  materialswireclipSelectList
} from "@/api/desginManage/ToolManagement.js";
const appStore = useAppStore();
const route = useRoute();
const closeDialog = () => {
  appStore.mapIndex = "";
};
const formData = ref({
  selectedOptions: [],
  selectedOptionsBg:[]
});
const activeName = ref('first')
const handleClick = () => {

}
const checkedNz = ref(true)
const options = ref({
  optionsNz: [],
  optionsBg: [],
})
const tableDataNz = ref([])
const tableDataBg = ref([])
const taskId= route.query.id;
// 合并单元格
const objectSpanMethodBg = ({row, column, rowIndex, columnIndex,}) => {
  if (column.property === 'wirecliptype') {
    if (rowIndex > 0) {
      const prevRow = tableDataBg.value[rowIndex - 1];
      if (prevRow[column.property] === row[column.property]) {
        return { rowspan: 0, colspan: 0 };
      }
    }
    let rowspan = 1;
    for (let i = rowIndex + 1; i < tableDataBg.value.length; i++) {
      if (tableDataBg.value[i][column.property] === row[column.property]) {
        rowspan++;
      } else {
        break;
      }
    }
    return { rowspan, colspan: 1 };
  }
}
const objectSpanMethodNz = ({row, column, rowIndex, columnIndex,}) => {
  if (column.property === 'wirecliptype') {
    if (rowIndex > 0) {
      const prevRow = tableDataNz.value[rowIndex - 1];
      if (prevRow[column.property] === row[column.property]) {
        return { rowspan: 0, colspan: 0 };
      }
    }
    let rowspan = 1;
    for (let i = rowIndex + 1; i < tableDataNz.value.length; i++) {
      if (tableDataNz.value[i][column.property] === row[column.property]) {
        rowspan++;
      } else {
        break;
      }
    }
    return { rowspan, colspan: 1 };
  }
}
const handleCheckChange = (row) => {
  const groupRows = tableDataBg.value.filter(
    item => item.wirecliptype === row.wirecliptype
  );

  groupRows.forEach(groupRow => {
    groupRow.checked = row.checked; // 同步选中状态
  });
};
const getCheckedItems = () => {
  return tableDataBg.value.filter(row => row.checked);
};
const saveXjpp=(type)=>{
formData.value.selectedOptions = tableDataNz.value.map(({option,taskid,template,materialswireclipid,...rest}) => ({taskid:taskId,template:'0',...rest}));
formData.value.selectedOptionsBg = tableDataBg.value.map(({option,taskid,template,materialswireclipid,...rest}) => ({taskid:taskId,template:'0',...rest}));
formData.value.selectedOptionsBg.forEach(element => {
  if(element.checked){
    element.selectstate=0
  }
});
let params={
  materialswireclips:type=='nz'?formData.value.selectedOptions:formData.value.selectedOptionsBg,
  taskid:taskId,
  wirecliptypekey:type
}
  AddDataBingGou(params).then(res=>{
    if(res.code==200){
        ElMessage.success(res.msg)
    }
  })
}
const dataList = async () => {
  // 并行获取两个数据
  const [naiZhangData, bingGouData] = await Promise.all([GetDataNaiZhang({taskId}), GetDataBingGou({taskId})]);
  // 处理 naiZhang 数据
  const wireclipTypeKeys = [...new Set(naiZhangData.data.map(item => item.wirecliptypekey))];
  // 获取相关材料信息
  const wireClipProjectRes = await getWireClipMaterialsProject(wireclipTypeKeys);
  tableDataNz.value = naiZhangData.data.map(item => {
    const matchingOptions = wireClipProjectRes.data
        .map(itemmap => itemmap[item.wirecliptypekey])
        .filter(Boolean); // 过滤掉 null 和 undefined
    return {
      ...item,
      option: matchingOptions.length > 0 ? matchingOptions[0] : []  // 如果没有匹配项，返回空数组
    };
  });

  const wireclipTypeKeysbingGou = [...new Set(bingGouData.data.map(item => item.wirecliptypekey))];
  // 获取相关材料信息
  const wireClipProjectResbingGou = await getWireClipMaterialsProject(wireclipTypeKeysbingGou);
  // 处理 bingGou 数据
  tableDataBg.value = bingGouData.data.map(item => {
    const matchingOptions = wireClipProjectResbingGou.data
        .map(itemmap => itemmap[item.wirecliptypekey])
        .filter(Boolean); // 过滤掉 null 和 undefined
    return {
      ...item,
      option: matchingOptions.length > 0 ? matchingOptions[0] : [],
      checked: false  // 如果没有匹配项，返回空数组
    };
  });
  console.log(tableDataNz.value, tableDataBg.value)
}
watch(
    () => appStore.mapIndex,
    (newInfo, oldInfo) => {
      console.log(newInfo, oldInfo);
      if (newInfo == "线夹匹配") {
        dataList()
      }
    }
);
</script>
<style scoped lang="scss">
@use '../../index' as *;
::v-deep .el-tabs__header {
  margin: 0;
}
</style>