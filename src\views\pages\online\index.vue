<script setup>
import OnlineDesign from "@/views/pages/online/onlineDesign.vue";
import Sidebar from "./sidebar";
import TopMenu from "./top-menu";
import { provide } from 'vue';
import useAppStore from "@/store/modules/app.js";
import { ElCollapseTransition } from 'element-plus'
import publicStore from "@/store/modules/publicStore.js"
const childRef = ref(null)
const callChildCMethod = () => {
  if (childRef.value) {
    childRef.value.MethodB();
  }
};
provide('callChildC', callChildCMethod);

const appStore = useAppStore();
const isCollapse = computed(() => !appStore.sidebar.opened);

const onlineDesignRef = ref()

const cadAppRef = ref()

provide('cadAppRef', cadAppRef);
const asideWidth = ref(265); // 初始宽度
let isResizing = false;
let startX = 0;
let startWidth = 0;
const startResizing = (event) => {
  event.preventDefault();
  let iframedoc = document.getElementById('MXCAD');
  if (iframedoc) {
    iframedoc.style.pointerEvents = 'none'; // 正确的语法
  }
  isResizing = true;
  startX = event.clientX;
  startWidth = asideWidth.value;
  jxWidth.value = 130
  document.addEventListener('mousemove', resizeAside);
  document.addEventListener('mouseup', stopResizing, { once: true });
};

const resizeAside = (event) => {
  if (isResizing) {
    const newWidth = startWidth + (event.clientX - startX);
    asideWidth.value = newWidth > 265 ? newWidth : 265; // 设置最小宽度为 50px
  }
};

const stopResizing = () => {
  let iframedoc = document.getElementById('MXCAD');
  if (iframedoc) {
    iframedoc.style.pointerEvents = 'auto'; // 正确的语法
  }
  isResizing = false;
  jxWidth.value = 5
  document.removeEventListener('mousemove', resizeAside);
  document.removeEventListener('mouseup', stopResizing);
};

onMounted(() => {
  document.addEventListener('mouseup', stopResizing);
  publicStore().childRef = childRef
  publicStore().taskId = new URLSearchParams(new URL(window.location.href).search).get('id')
});

onUnmounted(() => {
  document.removeEventListener('mouseup', stopResizing);
});
const jxWidth = ref(5)
</script>

<template>
  <div class="online-container">
    <el-container>
      <!--      <el-collapse-transition>-->
      <el-aside v-show="!isCollapse" :style="{ width: `${asideWidth}px` }">
        <sidebar ref="childRef" :style="{ width: `${asideWidth - 5}px` }" />
        <div class="resizer" :style="{ width: `${jxWidth}px` }" @mousedown="startResizing"></div>
      </el-aside>
      <!--      </el-collapse-transition>-->
      <el-container>
        <el-header style="height:50px">
          <top-menu />
        </el-header>
        <el-main>
          <online-design ref="onlineDesignRef" v-model:cad-app-ref="cadAppRef" />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style lang="scss" scoped>
.resizer {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  cursor: ew-resize;
  background-color: rgba(0, 0, 0, 0.2);
  user-select: none;
}

.online-container {
  height: 100vh;

  .el-container {
    height: 100%;
  }

  .el-aside,
  .el-header,
  .el-main {
    padding: 0;
  }

  .el-aside {
    margin-bottom: 0;
    position: relative;
  }
}
</style>
