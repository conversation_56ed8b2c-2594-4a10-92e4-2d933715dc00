import {
  McDbBlockReference,
  McDbLine,
  McDbPolyline,
  MxCADUiPrPoint,
  MxCpp,
  McDbAttributeDefinition,
  McDbAttribute,
  McGePoint3d,
  McDbObject,
  McDbEntity,
  McDbCurve, McDbCircle, McObjectId,
} from "mxcad";
import { calculateNewCoordinate } from "../../../utils/mx-cad";
// 绘制线路
export async function HzgctuBlockFun(formData, fileName, scale, attribDefList) {
  console.log("HzgctuBlockFun", formData);
  // 设置图块路径
  let blkFilePath =
    import.meta.url.substring(0, import.meta.url.lastIndexOf("/")) + fileName;
  console.log(blkFilePath);
  // 插入图块文件
  let mxcad = MxCpp.App.getCurrentMxCAD();
  let blkrecId = await mxcad.insertBlock(blkFilePath, formData.spanClass);
  if (!blkrecId.isValid()) {
    // 插入图块
    return { id: null, handle: null };
  }

  let blkRef = new McDbBlockReference();
  blkRef.blockTableRecordId = blkrecId;
  let box = blkRef.getBoundingBox();
  if (box.ret && formData.spanWidth) {
    let dLen = Math.abs(box.maxPt.x - box.minPt.x);
    blkRef.setScale((parseFloat(formData.spanWidth) / dLen) * 0.135);
  }

  let getPoint = new MxCADUiPrPoint();


  blkRef.position = new McGePoint3d(
    formData.points[0],
    formData.points[1],
    formData.points[2]
  );
  
  if (formData.rd) {
    blkRef.rotation = formData.rd;
  }
  if (formData.color) {
    blkRef.trueColor = formData.color;
  }
  let newBlkRefId = mxcad.drawEntity(blkRef);
  if (!newBlkRefId.isValid) {
    console.log("insert error");
    return { id: null, handle: null };
  }
  blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;

  // 如果块有属性定义，下面为块引创建属性定义。
  blkRef.disableDisplay(true);
  let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
  let ids = blkRecord.getAllEntityId();
  if (ids.length === 1 && ids[0].isKindOf("McDbBlockReference")) {
    const blockId = ids[0];
    const block = blockId.getMcDbEntity() as McDbBlockReference;
    const recordId = block.blockTableRecordId;
    blkRef.blockTableRecordId = recordId;
    blkRef.disableDisplay(true);
    setAttribute(newBlkRefId, recordId, attribDefList, formData);
  } else {
    setAttribute(newBlkRefId, blkrecId, attribDefList, formData);
  }
  // 获取该对象的实体
  const ent = newBlkRefId.getMcDbEntity();
  if (!ent) return { id: null, handle: null };
  // 获取对象ID
  const entId = ent.getObjectID();
  // 获取对象句柄
  const sHandle = ent.getHandle();
  // console.log("对象id", entId);
  // console.log("对象句柄", sHandle);
  // console.log("对象坐标", blkRef.position);
  if(formData.xData){
    formData.xData.forEach(item=> {
      ent.setxDataString(item.key, item.value);
    })
  }
  if(formData.TYSNG_info){
    formData.TYSNG_info.forEach(item=> {
      ent.setxDataString(item.key, item.value);
    })
  }
  ent.setxDataString("PointXY", formData.points);
  return { id: entId, handle: sHandle, pointsXYZ: blkRef.position };
}
function setMultiAttribute(blkRef:McDbBlockReference,tags:{tag:string;value?:string}[]){

for(const{tag,value=''} of tags ){
  let attrib=new McDbAttribute()
  attrib.tag=tag
  attrib.transformBy(blkRef.blockTransform);
  attrib = blkRef.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
}

}
function setAttribute(newBlkRefId, blkrecId, attribDefList, formData) {
  console.log(attribDefList)
  const blkRef = newBlkRefId.getMcDbEntity() as McDbBlockReference;
  // 如果块有属性定义，下面为块引创建属性定义。
  blkRef.disableDisplay(true);
  let blkRecord: any = blkrecId.getMcDbBlockTableRecord();
  let ids = blkRecord.getAllEntityId();
  // setMultiAttribute(blkrecId)
  ids.forEach((id: any, index: any) => {
    if (!id.isKindOf("McDbAttributeDefinition")) return;
    let attribDef = id.getMcDbEntity() as McDbAttributeDefinition;
    let tag = attribDef.tag;
    let attrib = new McDbAttribute();
    attrib.position = attribDef.position;
    attrib.alignmentPoint = attribDef.alignmentPoint;
    attrib.height = attribDef.height;
    attrib.trueColor = attribDef.trueColor;
    attrib.widthFactor = attribDef.widthFactor;

    if (attribDefList.length > 0) {
      // attribDef.tag='UserNumber'

      attribDefList.forEach((item) => {
        // console.log(tag,'----',item.name,item.value)
        if (item.name === tag) {
          attrib.textString = item.value;
            attrib.alignmentPoint = new McGePoint3d(
            attribDef.alignmentPoint.x+10,
            attribDef.alignmentPoint.y,
            attribDef.alignmentPoint.z
            )
        }
      });
    } else {
      attrib.textString = attribDef.textString;
        attrib.alignmentPoint = new McGePoint3d(
            attribDef.alignmentPoint.x+10,
            attribDef.alignmentPoint.y,
            attribDef.alignmentPoint.z
            )
    }
    // console.log(attribDef.tag,'attribDefattribDef',attrib.textString)
    attrib.tag = tag;
    if (formData.IsShowArr.length > 0) {
      formData.IsShowArr.forEach((item) => {
        if (item.TagName === tag) {
          attrib.isInvisible = item.IsShow === "1" ? false : true;
        }
      });
    } else {
      attrib.isInvisible = false;
    }
    let tags=[{tag:'UserNumber'},{tag:'Rotate'},{tag:'TowerHeght'},{tag:'TQMC'},{tag:'spec'},{tag:'TransCapacity'},{tag:'XH'},{tag:'Label'}]
    setMultiAttribute(blkRef,tags)
    attrib.transformBy(blkRef.blockTransform);
    attrib = blkRef.appendAttribute(attrib).getMcDbEntity() as McDbAttribute;
    attrib.textStyle = attribDef.textStyle;
    attrib.layer = attribDef.layer;
    // console.log(attrib.textString,'attrib.textString',attrib)
  });
  blkRef.disableDisplay(false);
}

interface Point3D {
  x: number;
  y: number;
  z?: number; // Z坐标可选
}
export async function drawLine(points: Point3D[], options?: any) {
  if (!points.length) return;
  const mxcad = MxCpp.App.getCurrentMxCAD();
  mxcad.getDatabase().setCurrentlyLayerName(options.legendtypekey);
  const polyline = new McDbPolyline();
  if(options?.legendtypekey === 'TY_DLD') {
    console.log('TY_DLDTY_DLDTY_DLD')
    // 设置线型
    // mxcad.addLinetype("MyLineType", "6,-10");
    // mxcad.drawLinetype = "MyLineType";
    const lineTypeName="MyLineType"
    const lineTypeDesc="6,-6"
    const ltId=mxcad.addLinetype(lineTypeName, lineTypeDesc);
    if(ltId&&ltId.isValid()){
        polyline.linetypeId=ltId
        polyline.linetypeScale=2.0
    }
    
  }
  // polyline.constantWidth = 0.5;
  console.log("options.color", options.color);
  if (options.color) {
    polyline.trueColor = options.color;
  }
  points.forEach((item, index) => {
    const { x, y, z } = item;

    polyline.addVertexAt(new McGePoint3d(x, y, z));
  });
  console.log("drawLinepolyline", polyline, options);
  // drawOffsetEntity([polyline], true, 5);
 /* if(options.xData) {
    options.xData.forEach(item=> {
      polyline.setxDataString(item.key, item.value);
    })
  }*/
  const entId = mxcad.drawEntity(polyline);
  const ent = entId.getMcDbEntity();
  if(options.xData) {
    options.xData.forEach(item=> {
      ent.setxDataString(item.key, item.value);
    })
  }
  // 获取该对象的实体
  return ent;
}

export async function drawMLine(points: Point3D[], options?: any) {
  console.log("drawMLine", points, options);
  if (!points.length) return;
  const mxcad = MxCpp.App.getCurrentMxCAD();
  mxcad.getDatabase().setCurrentlyLayerName(options.legendtypekey);
  const polyline = new McDbLine();
  let pt1 = new McGePoint3d(points[0].x, points[0].y, points[0].z)
  let pt2 = new McGePoint3d(points[1].x, points[1].y, points[1].z)
  polyline.startPoint = pt1
  polyline.endPoint = pt2
  if (options.color) {
    console.log('options.color', options.color)
    polyline.trueColor = options.color;
  }
  /*   const angle = calculateLineAngle(points[0], points[1]);
  const pt = calculateNewCoordinate(
    new McGePoint3d(points[0].x, points[0].y, 0),
    2,
    angle
  );
  console.log("offsetCurvespt", pt);
  console.log("polyline.offsetCurves(5, pt)", polyline.offsetCurves(5, pt));
  polyline.offsetCurves(5, pt).forEach((obj: McDbObject) => {
    console.log("offsetCurvesobj", obj);
    if (obj) mxcad.drawEntity(obj as McDbEntity);
  });
  console.log("angle", angle); */
  console.log("drawMLinepolyline", polyline);
  const lineIds =  drawOffsetEntity([polyline], true, 1)
  // console.log('lineEntList', lineEntList)
  const mPt = polyline.getPointAtDist(polyline.getLength().val / 2).val
  const dlgId = await drawDlg(mPt, options)

 /* const lineIds = lineEntList.map((item) => {
    console.log('lineIdsitem', item, item.getObjectID(), item.getOwnerID())
    return item.getObjectID()
  })*/
 const ent =dlgId.getMcDbEntity()
 ent.setxDataString('equipmentId', options.equipmentId)
  console.log('lineIds', lineIds)
  console.log('dlgEnt', dlgId)
  let aceepetArr = [dlgId, ...lineIds]
  console.log('aceepetArr', aceepetArr)
  console.log('options.equipmentId', options.equipmentId)
  aceepetArr.forEach((item: McObjectId) => {
    const ent = item.getMcDbEntity() as McDbEntity;
    if(ent && options.xData) {
      options.xData.forEach(item=> {
        ent.setxDataString(item.key, item.value);
      })
    }
  })
  mxcad.getDatabase().CreateGroup(aceepetArr, options.equipmentId);
  // mxcad.zoomCenter(13419275.828005916,3571584.597612434)
}

const drawDlg = async (pt: McGePoint3d, options: any) => {
  const mxcad = MxCpp.App.getCurrentMxCAD();
  const circle = new McDbCircle()
  circle.center = pt
  circle.radius = 1
  if (options.color) {
    circle.trueColor = options.color;
  }
  const ref = mxcad.drawEntity(circle)

  return ref
}

const drawOffsetEntity = (
  entityArr: McDbCurve[],
  isDrawOffsetCurve: boolean = true,
  offsetDist: number
): McDbCurve[] => {
  if (!entityArr.length) return;
  const mxcad = MxCpp.App.getCurrentMxCAD();
  const curveArr: McObjectId[] = [];
  // mxcad.getDatabase().setCurrentlyLayerName("MLine");
  entityArr.forEach((entity: McDbCurve) => {
    const pt = entity.getPointAtDist(entity.getLength().val / 2).val;
    const v = entity
      .getEndPoint()
      .val.sub(entity.getStartPoint().val)
      .perpVector()
      .normalize()
      .mult(offsetDist);
    const pt1 = pt.clone().addvec(v);
    const pt2 = pt.clone().subvec(v);
    console.log('entity.offsetCurves(offsetDist, pt1)',entity.offsetCurves(offsetDist, pt1), entity.getLength())
    entity.offsetCurves(offsetDist, pt1).forEach((item) => {

      if (isDrawOffsetCurve) {
        const ref = mxcad.drawEntity(item as McDbCurve);
        console.log('pt1ref', ref)
        curveArr.push(ref);
      }
    });
    entity.offsetCurves(offsetDist, pt2).forEach((item) => {

      if (isDrawOffsetCurve) {
        const ref = mxcad.drawEntity(item as McDbCurve);
        curveArr.push(ref);
      }
    });
  });
  return curveArr;
};

function calculateLineAngle(p1, p2) {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  const radians = Math.atan2(dy, dx);
  let degrees = radians * (180 / Math.PI);
  return degrees < 0 ? degrees + 360 : degrees; // 输出0-360°
}
