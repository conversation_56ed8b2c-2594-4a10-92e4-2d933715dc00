
import {
    insertFrameDownloadTu<PERSON>uang, insertFrameDownloadTuQian,
} from "@/api/desginManage/ToolManagement.js";
const crtkDwgUpdate = defineStore(
    'crtkDwgUpdate',
    {
        state: () => ({
            openUrlTk: undefined,
            openUrlTq: undefined
        }),
        actions: {
            mxPreview(val) {
                console.log('mxPreviewval',val);
                
                return new Promise((resolve, reject) => {
                    try {
                        insertFrameDownloadTuKuang(val.tfdx).then(resTk => {
                            if (resTk.type == 'application/json') {
                                // proxy.$message.warning('图框不存在');
                                return;
                            }
                            insertFrameDownloadTuQian(new URLSearchParams(new URL(window.location.href).search).get('id')).then(resTq => {
                                if (resTq.type == 'application/json') {
                                    // proxy.$message.warning('图签不存在');
                                    return;
                                }
                                // form.value.tzlxName = options.value.optionTzlx.find(item => item.id === form.value.tzlx).name;
                                console.log(resTk);
                                console.log(resTq);
                                
                                // this.openUrlTk = resTk
                                // this.openUrlTq = resTq
                                resolve({openUrlTk:resTk,openUrlTq:resTq})
                            })
                        })
                    } catch (error) {
                        reject()
                    }

                })
            },
        },
        persist: true
    })
export default crtkDwgUpdate
