
import publicStore from "@/store/modules/publicStore.js";
import useAppStore from "@/store/modules/app.js";
import {
    getMaterialsByIdAndUnitType,
    exportProjectmaterials,
} from "@/api/desginManage/ResultsManagement.js";
const appStore = useAppStore();
const zyclqdDwgUpdate = defineStore(
    'zyclqdDwgUpdate',
    {
        state: () => ({
            getZyTableData: undefined,
            oldCustomData: undefined,
        }),
        actions: {
            async getZycaqlData() {
                return new Promise((resolve, reject) => {
                    getMaterialsByIdAndUnitType({
                        projectId: new URLSearchParams(new URL(window.location.href).search).get('id'),
                        unitType: "ERP",
                        legendguidkeyListIn: appStore.property ? appStore.property : [],
                    }).then((res) => {
                        try {
                            if (res.code == 200) {
                                this.getZyTableData = res.data;
                                this.oldCustomData = res.data
                                    .filter((item) => item.dataType === "Custom")
                                    .map((item) => item);
                                resolve()
                            } else {
                                tableData.value = []
                                oldCustomData.value = []

                            }
                        } catch (error) {
                            reject(error)
                        }
                    });
                })
            },
            async jcylDwgUpdateFun(th) {
                publicStore().oniframeMessage({
                    type: "cadPreview",
                    content: "Mx_zyclqd",
                    formData: {
                        content: "材料清单生成报表",
                        tableData: JSON.stringify(this.getZyTableData),
                        countyOrganisationName: new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName'),
                        projectName: new URLSearchParams(new URL(window.location.href).search).get('projectName'),
                        stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                        proCode: new URLSearchParams(new URL(window.location.href).search).get('proCode'),
                        th: th
                    },
                });
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        const files = event.data.params.formData.files;
                        const tableData = JSON.parse(event.data.params.formData.tableData);
                        const tableDataNew = tableData.map((item) => {
                            return {
                                xh: 0,
                                dlms: "",
                                zlms: "",
                                xlms: "",
                                wlbm: item.materialcodeerp,
                                wlms: item.materialdescription,
                                kzbm: "",
                                kzms: "",
                                jsgfid: item.technicalprotocol,
                                dw: item.erpunit,
                                sqdj: 0.0,
                                sl: Number(item.quantity),
                                wllx: "",
                                dc: "",
                                sfyfj: "",
                                bz: "",
                            };
                        });
                        const fileFormData = new FormData();
                        files.forEach((item) => {
                            fileFormData.append("multipartFile", item);
                        });
                        fileFormData.append("zyclqcList", JSON.stringify(tableDataNew));
                        fileFormData.append("prjTaskInfoId", new URLSearchParams(new URL(window.location.href).search).get('id'));
                        fileFormData.append("stage", new URLSearchParams(new URL(window.location.href).search).get('stage'));
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        exportProjectmaterials(fileFormData).then((res) => {
                            if (res.code == 200) resolve()
                            // if (res.code === 200) {
                            //     proxy.$message.success("保存成功");
                            //     loading.close()
                            //     if (callChildC) {
                            //         callChildC();
                            //     }
                            // } else {
                            //     proxy.$message.error(res.msg);
                            //     loading.close()
                            // }
                        });
                    } catch (error) {
                        reject(error)
                    }

                })
            }
        },
        persist: true
    })
export default zyclqdDwgUpdate
