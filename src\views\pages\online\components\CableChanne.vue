<template>
  <data-dialog @close="closeDialog" dataWidth="700" v-if="tableList.length > 0">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        显示通道内电缆
      </h4>
    </template>
    <template #body>
      <el-table
        :data="tableList"
        @row-click="rowClick"
        highlight-current-row
        border
        class="input-table"
        size="small"
        style="width: 95%; margin: 10px 0 10px 2.5%"
      >
        <el-table-column
          align="center"
          label="当前线路"
          prop="name1"
        ></el-table-column>
        <el-table-column
          align="center"
          label="所属线路"
          prop="name2"
        ></el-table-column>
      </el-table>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
const { proxy } = getCurrentInstance();
const { sendMessage, cleanup } = useIframeCommunication();
import { inject } from "vue";
const route = useRoute();
const appStore = useAppStore();
const cadAppRef = inject("cadAppRef");
const taskId = route.query.id;
const stage = route.query.stage;
const cableDataList = ref([]);
const tableList = ref([]);
const select = ref({});
const closeDialog = () => {
  appStore.setCable([]);
  // appStore.clickData(false);
  tableList.value = [];
};
const rowClick = (e) => {
  select.value = e;
};

const onSubmit = () => {
  let options = [select.value.id];
  appStore.setPropertyInfo(select.value.id);
  sendMessage(
    cadAppRef.value,
    { type: "greeting", content: "SDK_click_device", options },
    (res) => {
      tableList.value = res.list;
    }
  );
};
watch(
  () => appStore.cableList,
  (newInfo, oldInfo) => {
    cableDataList.value = newInfo;
    if (cableDataList.value.length > 0) {
      appStore.clickData(true);
      let options = cableDataList.value.map((item) => ({
        className: "PWCableSecPSR",
        devId: item,
      }));
      sendMessage(
        cadAppRef.value,
        { type: "greeting", content: "SDK_taodao_details", options },
        (res) => {
          tableList.value = res.list;
        }
      );
    } else {
      tableList.value = [];
    }
  }
);
</script>

<style lang="scss" scoped>
@use "./index" as *;
.dialog-footer {
  float: right;
  margin: 10px;
}
</style>
