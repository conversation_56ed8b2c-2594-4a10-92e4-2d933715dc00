<template>
  <!-- 自动匹配图纸 -->
  <data-dialog v-if="appStore.mapIndex == '自动匹配图纸'" dataWidth="830px" @close="closeDialog">
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        自动匹配图纸
      </h4>
    </template>
    <template #body>
      <div class="photo-box">
        <div class="photo-left">
          <div class="photo-border">
            <span style="height: 30px; line-height: 30px; font-size: 14px;margin-left: 10px">图纸类别</span>
          </div>
          <div class="left-tree">
            <el-tree ref="treeRef" :data="treeData" :props="{ label: 'assessmenttype', children: 'children' }"
              default-expand-all node-key="drawingid" show-checkbox @node-click="handleNodeClick"
              @check-change="handleCheckChange">
              <template #default="{ node, data }">
                <span class="custom-node">
                  <img :src="data.children && data.children.length > 0 ? folderIcon : fileIcon" alt="icon"
                    class="custom-icon" />
                  <span ref="label" :style="{
                    width: '140px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }" class="custom-label">
                    <!-- 判断是否需要使用el-tooltip -->
                    <el-tooltip v-if="node.level > 2"
                      :content="data.drawingname == null ? data.assessmenttype : data.drawingname" class="box-item"
                      effect="dark" placement="top">
                      {{ data.drawingname == null ? data.assessmenttype : data.drawingname }}
                    </el-tooltip>

                    <!-- 如果文本未溢出，直接展示文本 -->
                    <span v-else>{{ data.drawingname == null ? data.assessmenttype : data.drawingname }}</span>
                  </span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="photo-right">
          <div class="photo-border">
            <span style="height: 30px; line-height: 30px; font-size: 14px;margin-left: 10px">预览</span>
          </div>
          <div style="height: 400px; overflow: hidden;">
            <div id="myiframeYl" style="height: 400px;"></div>
          </div>
        </div>
      </div>
      <div class="line" style="background: #fff; height: 50px">
        <div class="line-bnt" @click="InsertDrawing">插入图纸</div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import folderIcon from "@/assets/images/file8.png";
import fileIcon from "@/assets/images/file5.png";
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { tansParams } from "@/utils/ruoyi.js";
import { useRoute } from "vue-router";
const urlRoute = new useRoute()
const projectInfoStore = useProjectInfoStore();
import { getToken } from "@/utils/auth.js";
import {
  addTuZhiMuLu,
  autoMatching,
  autoMatchingInsertDrawing,
  autoMatchingPreview,
  insertFrameDownloadTuKuang, insertFrameDownloadTuQian,
  addDrawingMuLu
} from "@/api/desginManage/ToolManagement.js";
import { inject } from 'vue';
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { ElMessage } from "element-plus";
const callChildC = inject('callChildC');
let { proxy } = getCurrentInstance();
const route = useRoute();
const appStore = useAppStore();
const closeDialog = () => {
  appStore.mapIndex = "";
  const myiframe = document.getElementById("myiframeYl");
  const existingIframe = document.getElementById("MXCADYl");
  if (existingIframe) {
    myiframe.removeChild(existingIframe);
  }
};
const treeData = ref([])
const iframe = ref(null);
const treeRef = ref(null);
const checkList = ref([])
const handleCheckChange = (data, checked, indeterminate) => {
  const selectedLevel3Nodes = proxy.$refs.treeRef.getCheckedNodes()
  // 筛选出 level === 2 的节点
  const level2Ids = selectedLevel3Nodes.filter(node => {
    const level = getNodeLevel(node, treeData.value)
    return level === 3
  }).map(node => node)
  checkList.value = level2Ids
}

// 递归查找节点的 level
const getNodeLevel = (node, data, level = 1) => {
  for (let item of data) {
    if (item === node) return level
    if (item.children) {
      const childLevel = getNodeLevel(node, item.children, level + 1)
      if (childLevel) return childLevel
    }
  }
  return null
}

const InsertDrawing = () => {

  insertFrameDownloadTuKuang('tk12').then(resTk => {
    if (resTk.type == 'application/json') {
      proxy.$message.warning('图框不存在');
      return;
    }
    insertFrameDownloadTuQian(route.query.id).then(resTq => {
      if (resTq.type == 'application/json') {
        proxy.$message.warning('图签不存在');
        return;
      }
      const arrList = [];
      const stage = projectInfoStore.getProjectInfo('stage')
      const countyOrganisationName = projectInfoStore.getProjectInfo('countyOrganisationName')
      const projectName = projectInfoStore.getProjectInfo('projectName')
      const projectCode = projectInfoStore.getProjectInfo('projectCode')
      // 使用 Promise.all 来等待所有的异步操作完成
      Promise.all(checkList.value.map(async item => {
        const tzmc = item.drawingname
        const res = await autoMatchingPreview(item.drawingid);
        return {
          openUrl: res,
          openUrlTk: resTk,
          openUrlTq: resTq,
          sjdw: countyOrganisationName, // 单位
          gcmc: projectName, // 项目名称
          sjjd: stage === "1" ? "需求" : stage === "2" ? "可研" : stage === "3" ? "初设" : stage === "4" ? "施工" : stage === "5" ? "施工变更" : stage === "5" ? "竣工" : "", // 阶段
          time: min_formatDateTime(new Date(), 'yyyy-MM-dd'), // 时间
          th: projectCode, // 图号
          tzmc: tzmc, // 图纸名称
          tzbl: '1:1000', // 比例
          drawingid: item.drawingid
        };
      })).then(results => {
        arrList.push(...results); // 将结果添加到 arrList
        console.log(arrList, 'arrList');
        if (!arrList.length) return ElMessage.error('请选择！')
        oniframeMessage({
          type: "cadPreview",
          content: "Mx_zdTzfileTk",
          formData: {
            arrList: arrList
          }
        });
      });
    });
  });
}
const handleNodeClick = (item, node) => {
  if (node.level === 3) {
    autoMatchingPreview(item.drawingid).then(res => {
      oniframeMessage({
        type: "cadPreview",
        content: "Mx_cadPreview",
        formData: {
          openUrl: res,
        }
      })
    })
  }
}
// 将date转换为yyyy-MM-dd格式
const min_formatDateTime = (dateTime, type) => {
  const year = dateTime.getFullYear();
  const month = (dateTime.getMonth() + 1).toString().padStart(2, "0");
  const day = dateTime.getDate().toString().padStart(2, "0");
  const hours = dateTime.getHours().toString().padStart(2, "0");
  const minutes = dateTime.getMinutes().toString().padStart(2, "0");
  const seconds = dateTime.getSeconds().toString().padStart(2, "0");
  if (type == 'yyyy-MM-dd') {
    return `${year}-${month}-${day}`
  } else if (type == 'yyyy-MM-dd HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else if (type == 'yyyy-MM-dd HH:mm') {
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }
}
const dataList = () => {
  autoMatching({ taskid: route.query.id }).then(res => {
    if (res.code === 200) {
      console.log(res.data)
      treeData.value = [res.data]
    } else {
      proxy.$message.error(res.msg);
    }
  })
}
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = document.querySelector("#myiframeYl").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const handleMessage = async(event) => {
  
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '自动匹配图纸带有图框图签') {
      const files = event.data.params.formData.files
      const queryList = []
      for(const item of files){
        let content;
      await new Promise((resolve,reject)=>{
        let fileReader = new FileReader();
        console.log('fileReader111',fileReader)
        const fileRes = fileReader.readAsDataURL(item.file)
        fileReader.onload = function(){
        content = fileReader.result
        console.log('content111',content)
        if(content){
            resolve()
          }
        }
       })
        queryList.push({
          content:content,
          name: item.file.name,
          taskId: route.query.id,
          stage: route.query.stage,
          equipmentInfos: JSON.stringify({ drawingid: item.drawingid })
        })
      }
      console.log(queryList)
      addDrawingMuLu(queryList).then(res => {
        if (res.code === 200) {
          proxy.$message.success("操作成功");
        } else {
          proxy.$message.error(res.msg);
        }
        if (callChildC) {
          callChildC();
        }
      })
      // files.forEach(item => {
      //   fileFormData.append('files', item.file)
      // })
      // fileFormData.append('taskId', route.query.id)
      // fileFormData.append('stage', route.query.stage)
      // addTuZhiMuLu(fileFormData).then(res => {
      //   if (res.code === 200) {
      //     proxy.$message.success("操作成功");
      //   } else {
      //     proxy.$message.error(res.msg);
      //   }
      //   if (callChildC) {
      //     callChildC();
      //   }
      // })

      // autoMatchingInsertDrawing({ taskid: route.query.id, stage: route.query.stage, drawingIdList: checkList.value }).then(res => {
      //   if (res.code === 200) {
      //     proxy.$message.success("操作成功");
      //   } else {
      //     proxy.$message.error(res.msg);
      //   }
      //   if (callChildC) {
      //     callChildC();
      //   }
      // })
    }
  }
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    console.log(newInfo, oldInfo);
    if (newInfo == "自动匹配图纸") {
      dataList()
      const myiframe = document.getElementById("myiframeYl");
      const existingIframe = document.getElementById("MXCADYl");
      if (existingIframe) {
        myiframe.removeChild(existingIframe);
      }
      let params = tansParams({
        ...urlRoute.query,
        token: getToken() || urlRoute.query?.token,
        mod: 'preview'
      })
      const src = params.length !== 0
        ? `${import.meta.env.VITE_APP_MX_APP_SRC}?${params.endsWith('&') ? params.substring(0, params.length - 1) : params}`
        : `${import.meta.env.VITE_APP_MX_APP_SRC}`
      const iframeElement = document.createElement("iframe");
      iframe.value = iframeElement;
      nextTick(async () => {
        const myiframe = document.getElementById("myiframeYl");
        iframeElement.src = src
        iframeElement.id = "MXCADYl";
        iframeElement.style.width = "100%";
        iframeElement.style.height = "100%";
        myiframe.append(iframeElement);
        setTimeout(() => {
          oniframeMessage({
            type: "cadPreview",
            content: "Mx_cadPreview",
            formData: {
              openUrl: null,
            }
          })
        }, 2000)
      });
    }
  }
);
</script>
<style lang="scss" scoped>
@use '../../index' as *;
</style>
