import {
    getAllRodType,
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
import { getTuQianFileByPath } from "@/api/onlineDesign/index.js";
import { saveDwgInfo } from "@/api/insertSag/index.js"
const gxylDwgUpdate = defineStore(
    'gxylDwgUpdate',
    {
        state: () => ({
            cjwzTableData: undefined,
        }),
        actions: {
            async gxylDwgUpdateFun(th) {
                return new Promise(async (resolve, reject) => {
                    try {
                        const res = await getAllRodType({ taskId: publicStore().taskId });
                        let fileBlobArr = [];
                        if (res.code == 200) {
                            for (let i = 0; i < res.data.length; i++) {
                                if (res.data[i].drawPath) {
                                    res.data[i].fileBlob = await getTuQianFileByPath({
                                        filePath: res.data[i].drawPath,
                                    });
                                }
                                fileBlobArr.push({
                                    fileBlob: res.data[i].fileBlob,
                                    drawPath: res.data[i].drawPath,
                                });
                            }
                            resolve({ data: res.data, fileBlobArr: fileBlobArr, th: th })
                        }
                    } catch (error) {
                        reject(error)
                    }
                })
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        //保存报表文件到成果目录
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('files', item)// multipartFile
                        })
                        fileFormData.append("name", event.data.params.content === "GXYL" ? "杆塔型式一览图" : "基础型式一览图");
                        fileFormData.append('taskId', event.data.params.formData.proId)
                        fileFormData.append('stage', event.data.params.formData.stage)
                        saveDwgInfo(fileFormData).then(res => {
                            if (res.code == 200) resolve()
                            // if (res.code === 200) {
                            //     ElMessage.success("保存成功");
                            //     loading.close()
                            //     if (callChildC) {
                            //         callChildC();
                            //     }
                            // } else {
                            //     ElMessage.error(res.msg);
                            //     loading.close()
                            // }
                        })
                    } catch (error) {
                        reject(error)
                    }
                })
            }

        },
        persist: true
    })
export default gxylDwgUpdate
