<script setup>
import { useIframeCommunication } from "@/hooks/useIframeCommunication.js";
import { getTukuang, tzcfUploadFile } from "@/api/onlineDesign/index.js";
import { useRoute } from "vue-router";
import { ElLoading, ElMessage } from "element-plus";
import {inject} from "vue";
import useAppStore from "@/store/modules/app.js";

const appStore = useAppStore();

const callChildC = inject('callChildC');

const route = useRoute()
const taskId = computed(() => route.query.id)
const { sendMessage, cleanup } = useIframeCommunication()
const cadAppRef = inject('cadAppRef');

// 创建 loading 实例
const loading = ElLoading.service({
  lock: true,
  text: '拆分进行中...',
  background: 'rgba(0, 0, 0, 0.7)',
});

const getTuKuangData = async () => {
  try {
    const res = await getTukuang(taskId.value);
    sendHandle(res.data);
  } catch (error) {
    ElMessage.error('获取图框数据失败');
    loading.close(); // 在这里关闭 loading
  }
}

const sendHandle = (data) => {
  const msg = {
    cmd: 'Mx_DrawingSplitting',
    type: "sendStringToExecute",
    params: {
      data
    }
  };
  sendMessage(cadAppRef.value, msg, async (res) => {
    console.log('msg111',msg)
    console.log('res.', res);
    try {
      await uploadChange(res);
    } catch (error) {
      console.error("error：", error);
      ElMessage.error(error.message);
    } finally {
      loading.close(); // 确保在最后关闭 loading
    }
  });
}

const uploadChange = async (e) => {
  console.log('e.params',e.params)
  const fileList = e.params.fileList || [];
  const paramList = e.params.paramList || [];
  if (!fileList.length || !paramList.length) throw new Error('暂无数据！');

  const listJson = JSON.stringify(paramList);
  const formData = new FormData();
  formData.append("taskId", taskId.value);
  formData.append("listJson", listJson);
  fileList.forEach(item => {
    formData.append('file', item);
  });

  const res = await tzcfUploadFile(formData);
  if (res.code === 200) {
    ElMessage.success('保存成功！');
    callChildC()
  } else {
    throw new Error(res.msg); // 抛出错误以便在 catch 中处理
  }
}

onMounted(() => {
  getTuKuangData();
});
onUnmounted(() => {
  console.log('onUnmounted')
})
</script>

<template>
</template>
