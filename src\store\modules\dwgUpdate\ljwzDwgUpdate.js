import {
    generatorLjwzmxReport, inventoryMaterials
} from "@/api/insertSag/index.js";
import publicStore from "@/store/modules/publicStore.js";
const ljwzDwgUpdate = defineStore(
    'ljwzDwgUpdate',
    {
        state: () => ({
            tableData: undefined,
        }),
        actions: {
            getData(taskId) {
                return new Promise((resolve, reject) => {
                    try {
                        inventoryMaterials({ projectId: taskId, unitType: "ERP", materialStates: ["RemoveUsing", "NewUsing"] }).then(res => {
                            res.data.forEach((item, index) => {
                                item.number = index + 1
                            })
                            this.tableData = res.data
                            resolve()
                        })
                    } catch (error) {
                        reject(error)
                    }
                })
            },
            async dwgUpdateFun(th) {
                publicStore().oniframeMessage({
                    type: "cadPreview",
                    content: "Mx_ljqc",
                    formData: {
                        tableData: JSON.stringify(this.tableData),
                        countyOrganisationName: new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName'),
                        projectName: new URLSearchParams(new URL(window.location.href).search).get('projectName'),
                        stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                        proCode: new URLSearchParams(new URL(window.location.href).search).get('proCode'),
                        th: th
                    },
                });
            },
            message(event) {
                return new Promise((resolve, reject) => {
                    try {
                        const files = event.data.params.formData.files
                        console.log(files, 'filesfiles')
                        const tableData = JSON.parse(event.data.params.formData.tableData)
                        const tableDataNew = tableData.map((item, index) => {
                            return {
                                xh: index + 1,
                                wllb: item.materialCategory,
                                wlmc: item.materialName,
                                wlgg: item.materialSpecification,
                                ljsl: item.newReusedQuantity,
                                ccljsl: item.removedReusedQuantity,
                                dw: item.unit,
                                nx: "",
                                bz: item.remarks
                            }
                        })
                        const fileFormData = new FormData()
                        files.forEach(item => {
                            fileFormData.append('multipartFile', item)
                        })
                        fileFormData.append("ljwzmxList", JSON.stringify(tableDataNew));
                        fileFormData.append('prjTaskInfoId', new URLSearchParams(new URL(window.location.href).search).get('id'))
                        fileFormData.append('stage', new URLSearchParams(new URL(window.location.href).search).get('stage'))
                        // fileFormData.append('equipmentInfos', JSON.stringify({ th: `${route.query.proCode}-1` }))
                        generatorLjwzmxReport(fileFormData).then(res => {
                            if (res.code == 200) resolve()
                            // if (res.code === 200) {
                            //     proxy.$message.success("保存成功");
                            // } else {
                            //     proxy.$message.error(res.msg);
                            // }
                            // if (callChildC) {
                            //     callChildC();
                            // }
                        })
                    } catch (error) {
                        reject(error)
                    }
                })


            }

        },
        persist: true
    })
export default ljwzDwgUpdate
