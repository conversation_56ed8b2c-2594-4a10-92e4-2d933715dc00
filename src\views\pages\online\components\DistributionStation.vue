<template>
  <!-- 三.配电站房设计 -->
  <!-- 1.绘制路径图元 -->
  
  <data-dialog
    @close="closeDialog"
    dataWidth="440"
    v-if="appStore.mapIndex == '绘制路径图元'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        绘制路径图元
      </h4>
    </template>
    <template #body>
      <!-- <div
        style="
          padding: 0 10px;
          color: #282b33;
          font-weight: 400;
          font-size: 14px;
          height: 32px;
          display: flex;
          align-items: center;
          background: #e4f2f2;
          margin-bottom: 1px;
        "
      >
        <el-switch v-model="drawFlag" class="mb-2" />
        <span style="margin-bottom: 2px; margin-left: 5px"
          >按实际尺寸绘制路径图元</span
        >
      </div> -->
      <div v-show="storeForm.stationType === '1'">
        <el-row :gutter="10" class="line"><!-- 电压等级、方案类型 -->
          <el-col :span="4" class="line-item">
            <span> 方案 </span>
          </el-col>
          <el-col :span="8" class="line-input" style="justify-content: flex-start">
            <el-radio-group
              class="radio-group"
              @change="changeLj"
              v-model="storeForm.scheme"
              style="flex-wrap: nowrap"
            >
              <el-radio
                style="margin-right: 10px"
                value="10kV"
                label="10kV"
              ></el-radio>
              <el-radio
                style="margin-right: 10px"
                value="20kV"
                label="20kV"
              ></el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="12" class="line-input" style="justify-content: flex-start">
            <el-radio-group
              class="radio-group"
              v-model="storeForm.stationType"
              @change="changeStationType"
              style="
                flex-wrap: nowrap;
                border-left: white solid 2px;
                padding-left: 10px;
              ">
              <el-radio
                style="margin-right: 10px"
                value="1"
                label="箱式站"></el-radio>
              <el-radio
                style="margin-right: 10px"
                value="2"
                label="土建站"></el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="4" class="line-item"><!-- 方案类别 -->
            <span> 方案类别 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-select
              @change="changeSchemeType"
              v-model="storeForm.schemeType"
              class="in-item">
              <el-option
                v-for="item in draw.categoryList"
                :label="item.moduletypename"
                :value="item.moduletypekey"
                :key="item.moduletypekey"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="line-item"><!-- 方案编号 -->
            <span> 方案编号 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-select v-model="storeForm.schemeNum" class="in-item" @change="changeModuleCode">
              <el-option
                v-for="item in draw.schemeNumList"
                :label="item.modulecode"
                :value="item.modulecode"
                :key="item.moduleid"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="4" class="line-item"><!-- 方案名称 -->
            <span> 方案名称 </span>
          </el-col>
          <el-col :span="20" class="line-input">
            <el-select
              class="in-item"
              @change="changeSchemeName"
              v-model="storeForm.schemeName"
              placeholder="请选择"
            >
              <el-option
                v-for="item in draw.schemeNameList"
                :label="item.modulename"
                :value="item.moduleid"
                :key="item.moduleid"
              >
                <el-tooltip
                  :content="item.modulename"
                  placement="top"
                  effect="dark"
                >
                  <span>{{ item.modulename }}</span>
                </el-tooltip>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="4" class="line-item"><!-- 状态 -->
            <span> 状态 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.status"
              placeholder="请选择"
            >
              <el-option label="新建" value="New"></el-option>
              <el-option label="原有" value="Original"></el-option>
              <el-option label="拆除" value="Remove"></el-option>
              <el-option label="改造" value="Reform"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="line-item"><!-- 站房编号 -->
            <span>站房编号</span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              v-model="storeForm.stationNum"
              placeholder="请输入站房编号"
            ></el-input>
          </el-col>
          <!-- 是否包含通信 浙江暂时没有-->
          <!-- <el-col :span="3" class="line-item">
            <span>是否包含通信</span>
          </el-col>
          <el-col :span="5" class="line-input">
            <el-select v-model="storeForm.isPrecasted" placeholder="请选择">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-col> -->
          <!-- 基础状态 浙江暂时没有-->
          <!-- <el-col :span="3" class="line-item">
            <span>基础状态</span>
          </el-col>
          <el-col :span="5" class="line-input">
            <el-select v-model="storeForm.status" placeholder="请选择">
              <el-option label="新建" value="New"></el-option>
              <el-option label="原有" value="Original"></el-option>
              <el-option label="拆除" value="Remove"></el-option>
              <el-option label="改造" value="Reform"></el-option>
            </el-select>
          </el-col> -->
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="4" class="line-item"><!-- 土质类型 -->
            <span>土质类型</span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.soilType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in draw.tzlxList"
                :label="item.value"
                :value="item.key"
                :key="item.key"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="line-item"><!-- 是否土建预制-->
            <span> 是否土建预制 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.Sftjyz"
              placeholder="请选择"
            >
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-col>
          <!-- 接地状态 浙江暂时没有-->
          <!-- <el-col :span="3" class="line-item">
            <span> 接地状态 </span>
          </el-col>
          <el-col :span="5" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.jdStatus"
              placeholder="请选择"
            >
              <el-option label="新建" value="New"></el-option>
              <el-option label="原有" value="Original"></el-option>
              <el-option label="拆除" value="Remove"></el-option>
              <el-option label="改造" value="Reform"></el-option>
            </el-select>
          </el-col> -->
        </el-row>
        <!-- 破碎路面类型 破碎路面面积 智能融合终端 -->
        <el-row :gutter="10" class="line">
          <el-col :span="4" class="line-item">
            <span> 破碎路面类型 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.roadType"
              placeholder="请选择"
            >
              <el-option label="混凝土路面" value="混凝土路面"></el-option>
              <el-option
                label="沥青混凝土路面"
                value="沥青混凝土路面"
              ></el-option>
              <el-option
                label="砖石路面（包括碎石、砖块、花岗岩等）"
                value="砖石路面（包括碎石、砖块、花岗岩等）"
              ></el-option>
              <el-option label="普通草坪修复" value="普通草坪修复"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="line-item">
            <span> 破碎路面面积 </span>
          </el-col>
          <el-col :span="8" class="line-input">
            <el-input
              type="number"
              min="0"
              class="in-item"
              v-model="storeForm.roadArea"
              placeholder=""
            ></el-input>
          </el-col>
          <!-- <el-col :span="3" class="line-item">
            <span> 智能融合终端 </span>
          </el-col>
          <el-col :span="5" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.ZNRHZD"
              placeholder="请选择"
            >
              <el-option label="包含" value="包含"></el-option>
              <el-option label="不包含" value="不包含"></el-option>
            </el-select>
          </el-col> -->
        </el-row>
      </div>
      <div v-show="storeForm.stationType === '2'">
        <el-row :gutter="10" class="line">
          <el-col :span="8" class="line-item">
            <span> 方案 </span>
          </el-col>
          <el-col :span="16" class="line-input" style="justify-content: flex-start">
            <el-radio-group
              class="radio-group"
              @change="changeLj"
              v-model="storeForm.scheme"
              style="
              flex-wrap: nowrap;
              "
            >
              <el-radio
              style="margin-right: 10px"
              value="10kV"
              label="10kV"
              ></el-radio>
              <el-radio
              style="margin-right: 10px"
              value="20kV"
              label="20kV"
              ></el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="8" class="line-item">
            <span> 方案类型 </span>
          </el-col>
          <el-col :span="16" class="line-input" style="justify-content: flex-start">
            <el-radio-group
              class="radio-group"
              @change="changeStationType"
              v-model="storeForm.stationType"
              style="flex-wrap: nowrap;"
            >
              <el-radio
                style="margin-right: 10px"
                value="1"
                label="箱式站"
              ></el-radio>
              <el-radio
                style="margin-right: 10px"
                value="2"
                label="土建站"
              ></el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="8" class="line-item"><!-- 方案类别 -->
            <span> 方案类别 </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-select
              @change="changeSchemeType"
              v-model="storeForm.schemeType"
              class="in-item"
            >
              <el-option
                v-for="item in draw.categoryList"
                :label="item.moduletypename"
                :value="item.moduletypekey"
                :key="item.moduletypekey"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line"><!-- 方案编号 -->
          <el-col :span="8" class="line-item">
            <span> 方案编号 </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-select v-model="storeForm.schemeNum" class="in-item" @change="changeModuleCode">
              <el-option
                v-for="item in draw.schemeNumList"
                :label="item.modulecode"
                :value="item.modulecode"
                :key="item.moduleid"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line"><!-- 状态 -->
          <el-col :span="8" class="line-item">
            <span> 状态 </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.status"
              placeholder="请选择"
            >
              <el-option label="新建" value="New"></el-option>
              <el-option label="原有" value="Original"></el-option>
              <el-option label="拆除" value="Remove"></el-option>
              <el-option label="改造" value="Reform"></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line"><!-- 方案名称 -->
          <el-col :span="8" class="line-item">
            <span> 方案名称 </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.schemeName"
              placeholder="请选择"
            >
              <el-option
                v-for="item in draw.schemeNameList"
                :label="item.modulename"
                :value="item.moduleid"
                :key="item.moduleid"
              >
                <el-tooltip
                  :content="item.modulename"
                  placement="top"
                  effect="dark"
                >
                  <span>{{ item.modulename }}</span>
                </el-tooltip>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line">
          <el-col :span="8" class="line-item"><!-- 站房编号 -->
            <span>站房编号</span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-input
              v-model="storeForm.stationNum"
              placeholder="请输入站房编号"
            ></el-input>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line"><!-- 土质类型 -->
          <el-col :span="8" class="line-item">
            <span> 土质类型 </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.soilType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in draw.tzlxList"
                :label="item.value"
                :value="item.key"
                :key="item.key"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line"><!-- 破碎路面类型 -->
          <el-col :span="8" class="line-item">
            <span> 破碎路面类型 </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-select
              class="in-item"
              v-model="storeForm.roadType"
              placeholder="请选择"
            >
              <el-option label="混凝土路面" value="混凝土路面"></el-option>
              <el-option
                label="沥青混凝土路面"
                value="沥青混凝土路面"
              ></el-option>
              <el-option
                label="砖石路面（包括碎石、砖块、花岗岩等）"
                value="砖石路面（包括碎石、砖块、花岗岩等）"
              ></el-option>
              <el-option label="普通草坪修复" value="普通草坪修复"></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="line"><!-- 破碎路面积 -->
          <el-col :span="8" class="line-item">
            <span> 破碎路面积(m²) </span>
          </el-col>
          <el-col :span="16" class="line-input">
            <el-input
              type="number"
              min="0"
              class="in-item"
              v-model="storeForm.roadArea"
              placeholder=""
            ></el-input>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="10" class="line" v-if="drawFlag">
        <el-col :span="4" class="line-item"
          ><!-- 长 -->
          <span>长 </span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input
            type="number"
            min="0"
            class="in-item"
            v-model="storeForm.long"
            placeholder=""
          ></el-input>
        </el-col>
        <el-col :span="4" class="line-item"
          ><!-- 宽 -->
          <span> 宽 </span>
        </el-col>
        <el-col :span="8" class="line-input">
          <el-input
            type="number"
            min="0"
            class="in-item"
            v-model="storeForm.width"
            placeholder=""
          ></el-input>
        </el-col>
      </el-row>
      <div style="background: #e4f2f2; height: 50px; justify-content: center;" class="line">
        <div class="line-bnt" @click="drawStationClick">绘制</div>
      </div>
    </template>
  </data-dialog>

  <!-- 变压器增容 -->
  <data-dialog
    dataWidth="398"
    @close="closeDialog"
    v-if="appStore.mapIndex == '变压器增容'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        变压器增容
      </h4>
    </template>
    <template #body>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div class="title-color"></div>
        <div class="title-text">当前设备参数</div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 型号 </span>
        </div>
        <div class="line-input">
          <span class="in-item" style="background: white;font-weight: normal;">{{ byqForm.model }}</span>
        </div>
      </div>
      <div
        style="
          background: #e4f2f2;
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          margin-bottom: 1px;
        "
      >
        <div class="title-color"></div>
        <div class="title-text">增容参数</div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 柱上变台方案 </span>
        </div>
        <div class="line-input">
          <el-select
              @change="changeFangAn"
              v-model="byqForm.scheme"
              class="in-item">
              <el-option
                v-for="item in fangAnList"
                :label="item.moduletypename"
                :value="item.moduletypekey"
                :key="item.moduletypekey"
              ></el-option>
            </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 型号 </span>
        </div>
        <div class="line-input">
          <el-select
              v-model="byqForm.model1"
              class="in-item">
              <el-option
                v-for="item in mkList"
                :label="item.modulename"
                :value="item.modulename"
                :key="item.modulename"
              ></el-option>
            </el-select>
        </div>
      </div>
      <div style="background: #e4f2f2;  height: 50px;justify-content: center" class="line">
        <div class="line-bnt"  @click="saveChange">保存</div>
      </div>
      <div><span style="color: red;margin-left: 10px;">注：选中的必须是存量数据。</span></div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { getDicts } from "@/api/desginManage/GccsDialog.js";
import { getPropertyByIdAndKey } from "@/api/config-management";
import {
  getSchemeType,
  getSchemeModuleCode,
  GetIntervalSplicingInfo,
  getIntervalNumData
} from "@/api/baseDate/onlineDesign";
import {getPropertyData} from "@/api/onlineDesign/index.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { drawEndStation } from "../sdkDrawBackData/stationModuleSDK"
import { intervalAddFile,readStationData,getFangAnList,getMuKuaiList } from "@/api/insertSag/index.js";
import { inject } from 'vue';
import { useRoute } from "vue-router";
const { sendMessage, cleanup } = useIframeCommunication();
const appStore = useAppStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
//  三.配电站房设计
//定义 绘制路径图元 form表单
const storeForm = ref({
  scheme: "10kV", //方案
  stationType: "1", //站房类型
  schemeType: "", //方案类别
  schemeName: "", //方案名称
  schemeNum: "", //方案编号
  status: "", //状态
  stationNum: "", //站房编号
  soilType: "", //土质类型
  roadType: "", //破碎路面类型
  roadArea: "", //破碎路面积(m²)
  isPrecasted: "", //是否包含通信
  Sftjyz: "", //是否土建预制
  materialInfo: "", //物料信息
  ZNRHZD: "",//智能融合终端
  long: "", //长
  width: "", //宽
  jgxx:[],//进出线统计
});

const byqForm=ref({
  module:'',
  scheme:'',
  module1:''
})

//!绘制路径图元 --- start
const drawFlag = ref(false);
const draw = ref({
  tzlxList: [],
  categoryList: [],
  schemeNumList: [],
  materialInfoList: [],
  inNum: 0,
  outNum: 0,
});

// 更改电压等级
const changeLj = (e) => {
  getSchemeType({
    type: storeForm.value.stationType,
    voltage: storeForm.value.scheme,
  }).then((res) => {
    console.log("getSchemeType", res);
    draw.value.categoryList = res.data;
    storeForm.value.schemeType = res.data[0].moduletypekey;
    changeSchemeType(res.data[0].moduletypekey);
  });
};
// 更改站房类型
const changeStationType = (e) => {
  changeLj(null);
};
// 方案类别
const changeSchemeType = (e) => {
  console.log(e,'方案类别')
  const stationInfo = draw.value.categoryList.find(
    (o) => o.moduletypekey === e
  );
  storeForm.value.symbolId = stationInfo.legendtype?.symbolId;
  storeForm.value.sdkClassname = stationInfo?.legendtype?.sdkClassname.split("-")[0];
  console.log("方案类别改变", e);
  getSchemeModuleCode({
    type: storeForm.value.stationType,
    voltage: storeForm.value.scheme,
    moduleTypeKey: e,
  }).then((res) => {
    draw.value.schemeNumList = res.data;
    storeForm.value.schemeNum = res.data[0].modulecode;
    changeIntervalSplicingInfo(res.data[0].modulecode)
  });
};
//方案编号
const changeModuleCode = (e) => {
  changeIntervalSplicingInfo(e)
}
//方案名称改变
const changeIntervalSplicingInfo = (e) => {
  GetIntervalSplicingInfo({
      moduleTypeKey: storeForm.value.schemeType,
      moduleCode: e,
    }).then((res) => {
      draw.value.schemeNameList = res.data.filter(
        (item) => item.voltage === storeForm.value.scheme
      );
      storeForm.value.schemeName =
        draw.value.schemeNameList.length > 0
          ? draw.value.schemeNameList[0].moduleid
          : "";
      changeSchemeName(storeForm.value.schemeName)
    });
};
const cadAppRef = inject('cadAppRef');

//方案名称改变
const changeSchemeName = (e) => {
  console.log("方案名称改变 获取进出线数", e);
  getIntervalNumData({
    id: e,
  }).then((res) => {
    draw.value.inNum = res.data?.inNum ?? 0
    draw.value.outNum = res.data?.outNum ?? 0
  });
};
// 绘制
const drawStationClick = async () => {
  if (
    storeForm.value.schemeName == "" ||
    storeForm.value.symbolId == "" ||
    storeForm.value.sdkClassname == "" ||
    storeForm.value.scheme == ""
  ) {
    proxy.$message.error("请填写完整信息");
  } else {
    // 获取几进几出 storeForm.value.schemeName, JCXXS
    // const res = await getPropertyByIdAndKey({
    //   id: storeForm.value.schemeName,
    //   key: "JCXXS",
    // });
    // console.log("getPropertyByIdAndKey", res);
    // if (!res.data) {
    //   proxy.$message.error("数据有误，未能查询到间隔信息");
    //   return;
    // }
    const sdkStationId = draw.value.schemeNameList.find(o=>o.moduleid === storeForm.value.schemeName).sdkStationId
    const sdkStationName = draw.value.categoryList.find(o=>o.moduletypekey === storeForm.value.schemeType).moduletypename
    const options = {
      symbolId: storeForm.value.symbolId ?? "252301", // 站房图元ID
      symbolClassName: storeForm.value.sdkClassname ?? "PWPreComSubstationPSR", // 站房类型
      stationName: sdkStationName, // 站房名称
      aliasname: storeForm.value.stationNum, // 站房简称
      voltageLevel: storeForm.value.scheme === "20kV" ? 24 : 22, // 电压等级代码
      info: [
        {// 站房内相关信息，一个对象代表一段母线
          bayInfo: [
            // 间隔信息，一个对象代表一个间隔
            // 间隔类型，间隔名称，间隔编号
            { bayType: "4", bayName: "PT柜", bayNo: "G1" },
          ],
          mlInfo: {
            NAME: storeForm.value.stationNum + "Ⅰ段母线", // 母线名称
            ALIAS_NAME: "Ⅰ段母线", // 母线简称
          },
        },
      ],
      sdkStationId
    };
    for (let o = 0; o < draw.value.inNum; o++) {
      options.info[0].bayInfo.push({
        bayType: "6",
        bayName: "进线柜",
        bayNo: "G" + (options.info[0].bayInfo.length + 1),
      });
    }
    for (let o = 0; o < draw.value.outNum; o++) {
      options.info[0].bayInfo.push({
        bayType: "1",
        bayName: "出线柜",
        bayNo: "G" + (options.info[0].bayInfo.length + 1),
      });
    }
    if(draw.value.inNum === draw.value.outNum && draw.value.outNum === 0){
      options.info[0].bayInfo = []

    options.info[0].bayInfo=[  { bayType: "1", bayName: "出线柜", bayNo: "G1" }]
    }
    sendMessage(cadAppRef.value,{ type: "greeting", content: "SDK_zfty", options }, (res) => {
      if(res.cmd === 'SDK_zfty'){
        console.log('SDK_zfty回调', res)
        const legendtypekey = draw.value.categoryList.find(o=>o.moduletypekey === storeForm.value.schemeType).legendtype.legendtypekey
        drawEndStation(res.params.status === "000000" && res.params.result, storeForm.value, {
          sdkClassName: options.symbolClassName,
          legendtypekey,
          bayInfo: options.info[0].bayInfo
        })
      }
    });
  }
};
const fangAnList=ref([])
const mkList=ref([])
const zrList=ref([])
// !柱上变台方案 --- start
const getFAList=(vol)=>{
  getFangAnList({voltage:vol}).then((res) => {
    if(res.code === 200 && res.data){
      fangAnList.value = res.data
      byqForm.value.scheme=res.data[0].moduletypekey
      getMKList(byqForm.value.scheme)
    }
    if(appStore.sdkClassName?.clickItemClassName=="PWOPTransformerPSR"){
      const data1 = {
  "legendguidkeys": [appStore.sdkClassName.clickItem],
  "taskid": route.query.id
};
      getPropertyData(data1).then(res=>{
       if(!res.data){
        let options={
        className: appStore.sdkClassName.clickItemClassName,
          devId: appStore.sdkClassName.clickItem,
      }
      sendMessage(cadAppRef.value,{ type: "greeting", content: "SDK_byqzr", options }, (res) => {
        zrList.value=res.params.result.P_EquipmentToAsset[0].tempDatas
      byqForm.value.model=res.params.result.P_EquipmentToAsset[0].tempDatas[0].MODEL
    });
       }
      })
    }
  })
}



//增容保存
const saveChange=()=>{
  if( zrList.value&&byqForm.value.model1&&byqForm.value.model){
     zrList.value[0].MODEL=byqForm.value.model1
     zrList.value[0].EDIT_TYPE='U'
     zrList.value[0].G_EDIT_TYPE='U'
  sendMessage(cadAppRef.value,{ type: "greeting", content: "SDK_zrsave", options:JSON.stringify(zrList.value[0]) }, (res) => {
    });
  }else{
    proxy.$message.warning('当前型号为空或选择的不是存量数据！')
  }
}

const changeFangAn=(e)=>{
  getMKList(e)
}

const getMKList=(e)=>{
  getMuKuaiList({gangao:'12',moduletypekey:e}).then(res=>{
    mkList.value=res.data
   if(res.data.length>0){
    byqForm.value.model1=res.data[0].modulename
   } else{
    byqForm.value.model1=''
   }
}) 
}
// !绘制路径图元 --- end
const closeDialog = () => {
  appStore.mapIndex = "";
};
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    console.log(newInfo,'newInfo')
    if (newInfo == "绘制路径图元") {
      storeForm.value.stationNum=''
      storeForm.value.roadArea = "";
      storeForm.value.roadType = "混凝土路面";
      storeForm.value.status = "New";
      storeForm.value.jdStatus = "New";
      storeForm.value.isPrecasted = "否";//是否包含通信
      storeForm.value.Sftjyz = "否";//是否土建预制
      storeForm.value.long = 10;
      storeForm.value.width = 10;
      storeForm.value.scheme = "10kV";
      drawFlag.value = false;
      getDicts({ dictionarKeys: ["Geological"] }).then((res) => {
        if (res.code === 200 && res.data) {
          draw.value.tzlxList = res.data.Geological;
          storeForm.value.soilType = res.data.Geological[0].key;
        }
      });
      changeLj(storeForm.value.scheme);
    }else if(newInfo == "变压器增容"){
      getFAList('10kV')
       byqForm.value.model=''
      zrList.value=[]
    }
  }
);
</script>

<style scoped lang="scss">
::v-deep .el-select {
  width: 257px;
  height: 27px;
}

::v-deep .el-form-item.el-form-item--default {
  margin-bottom: 0;
  line-height: 0;
}

::v-deep .el-form-item--default .el-form-item__content {
  line-height: normal;
}
::v-deep .el-form-item__error {
  right: 22px !important;
  top: 12px !important;
  left: auto;
}
::v-deep .el-select__placeholder {
  color: #282b33;
  font-weight: 400;
}
::v-deep .el-input__inner {
  color: #282b33;
  font-weight: 400;
}
h4 {
  font-family: "Noto Sans SC", sans-serif;
}

.title-text {
  color: #008486;
  font-weight: bold;
  font-family: "Noto Sans SC", sans-serif;
}
.check_radio {
  ::v-deep .el-checkbox__label {
    color: #282b33;
    font-weight: 400;
  }
}
.line {
  display: flex;
  justify-content: flex-start;
  // justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  width: 100%;
  :last-child {
    margin-bottom: 0;
  }
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .tw-ipt {
    width: 84px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    // span {
    //   width: 60px;
    // }
  }
  .radio-group {
    color: #282b33;
    font-weight: 400;
    ::v-deep .el-radio__label {
      color: #282b33;
      font-weight: 400;
    }
  }
  .checkbox-group {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }
  
  .input-table {
    margin-top: -2px;
    margin-bottom: 1px;
  }
  .tw-sct {
    width: 105px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    .in-item {
      width: 82px;
      height: 27px;
    }
  }
  .line-item {
    flex: 1;
    height: 40px;
    background: #b5dddd;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
  }
  .line-input {
    flex: 3;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 5px;
    .in-item {
      width: 100%;
      height: 27px;
    }
  }
  .line-no-display {
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }
  .radio-input {
    width: 294px;
    height: 60px;
    display: flex;
    background: #e4f2f2;
    flex-direction: column;
    .in-item {
      height: 20px;
      ::v-deep .el-input__wrapper {
        width: 150px;
      }
    }
  }
  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}

// 标注样式
.bz-line {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1px;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;
  height: 150px;
  .bz-left {
    width: 100px;
    margin-left: 10px;
    .bz-border {
      padding: 0 10px;
      border: #69b5b6 2px solid;
      height: 120px;
    }
  }
  .bz-right {
    width: 500px;
    margin-left: 10px;
    .input-table {
      margin-top: -2px;
      margin-bottom: 1px;
    }
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
.material {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .active {
    background: #0e8b8d !important;
    color: white !important;
  }
}
.small-material {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 11px !important;
    color: #ffffff;
    font-weight: 600;
  }
  ::v-deep .el-table td .el-table__cell div {
    font-size: 10px !important;
    font-weight: 600;
  }
}
.meter-draw {
  ::v-deep .el-table .el-table__header-wrapper th {
    background: #50a9aa !important;
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
  }
  .draw {
    font-size: 12px;
    color: #282b33;
    font-weight: bold;

    span {
      margin-left: 10px;
    }
  }
}
.title-color {
  width: 4px;
  height: 15px;
  background: #0e8b8d;
  margin: 0 10px;
}
.draw-footer {
  margin: 0 10px 10px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid #badddd;
}
.photo-box {
  padding: 10px;
  display: flex;
  height: 450px;
  justify-content: flex-start;
  background: #e4f2f2;
  .photo-left {
    width: 200px;
    height: 430px;
    .left-tree {
      border: 2px solid #badddd;
      padding: 10px;
      height: 400px;
      .el-tree {
        background: transparent;
      }
      ::v-deep .el-tree-node:focus > .el-tree-node__content {
        background: rgba(80, 169, 170, 0.4);
      }
      ::v-deep .el-tree-node :hover {
        background: rgba(80, 169, 170, 0.1) !important;
      }
    }
  }
  .photo-right {
    margin-left: 10px;
    width: 600px;
    height: 430px;
    .photo-border {
      height: 30px;
      width: 600px;
      border: 2px solid #badddd;
    }
    .photo-table {
      width: 350px;
      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }
    .photo-img {
      margin-top: -2px;
      width: 250px;
      background: white;
    }
  }
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background: #fff;
}
/* 修改Element UI中el-checkbox选中时的对号颜色 */
::v-deep .el-checkbox__inner:after {
  border-color: #07888a; /* 将对号颜色改为红色 */
}
</style>
