import publicStore from "@/store/modules/publicStore"
import cjwzDwgUpdate from "@/store/modules/dwgUpdate/cjwzDwgUpdate"
import gxylDwgUpdate from "@/store/modules/dwgUpdate/gxylDwgUpdate"
import jcylDwgUpdate from "@/store/modules/dwgUpdate/jcylDwgUpdate"
import zyclqdDwgUpdate from "@/store/modules/dwgUpdate/zyclqdDwgUpdate"
import dlmxDwgUpdate from "@/store/modules/dwgUpdate/dlmxDwgUpdate"
import gtmxDwgUpdate from "@/store/modules/dwgUpdate/gtmxDwgUpdate"
import ggmxDwgUpdate from "@/store/modules/dwgUpdate/ggmxDwgUpdate"
import ljwzDwgUpdate from "@/store/modules/dwgUpdate/ljwzDwgUpdate"
import dltdtjDwgUpdate from "@/store/modules/dwgUpdate/dltdtjDwgUpdate"
import crtkDwgUpdate from "@/store/modules/dwgUpdate/crtkDwgUpdate"
import zdppDwgUpdate from "@/store/modules/dwgUpdate/zdppDwgUpdate"
export const upDateFunList = [
    {
        tm: "拆旧物资明细表",
        fun: (th) => {
            return new Promise(async (resolve, reject) => {
                await cjwzDwgUpdate().getCjwzTableData(publicStore().taskId)
                await cjwzDwgUpdate().unDateCjwzExcel(publicStore().taskId);
                console.log(window.location.href);
                publicStore().oniframeMessage({
                    type: "cadPreview",
                    content: "Mx_cjwz",
                    formData: {
                        tableData: JSON.stringify(cjwzDwgUpdate().cjwzTableData.filter(item => item.addTag !== 1)),
                        countyOrganisationName: new URLSearchParams(new URL(window.location.href).search).get('countyOrganisationName'),
                        projectName: new URLSearchParams(new URL(window.location.href).search).get('projectName'),
                        stage: new URLSearchParams(new URL(window.location.href).search).get('stage'),
                        proCode: new URLSearchParams(new URL(window.location.href).search).get('proCode'),
                        th: th
                    },
                });
                resolve()
            })
        },
    },
    {
        tm: "杆塔型式一览图",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                const res = await gxylDwgUpdate().gxylDwgUpdateFun(th)
                publicStore().postMessageMsgCadReport({ type: 'MsgCad', content: "GXYL", th: th }, res.data, res.fileBlobArr);
                resolve()
            })
        }
    },
    {
        tm: "基础型式一览图",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                const res = await jcylDwgUpdate().jcylDwgUpdateFun(th)
                publicStore().postMessageMsgCadReport({ type: 'MsgCad', content: "JCYL", th: th }, res.data, res.fileBlobArr);
                resolve()
            })
        }
    },
    {
        tm: "主要材料清单",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                await zyclqdDwgUpdate().getZycaqlData()
                zyclqdDwgUpdate().jcylDwgUpdateFun(th)
                resolve()
            })
        }
    },
    {
        tm: "杆塔明细表",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                const res = await gtmxDwgUpdate().gtmxDwgUpdateFun(th)
                publicStore().postMessageMsgCadReport({ th: th, type: "MsgCad", content: "GTMX", }, res.data, []);
                resolve()
            })
        }
    },
    {
        tm: "电缆明细表",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                await dlmxDwgUpdate().getDlmxData(publicStore().taskId)
                dlmxDwgUpdate().dlmxDwgUpdateFun(th)
                resolve()
            })
        }
    },
    {
        tm: "钢管杆明细表",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                await ggmxDwgUpdate().getData(publicStore().taskId)
                ggmxDwgUpdate().dwgUpdateFun(th)
                resolve()
            })
        }
    },
    {
        tm: "利旧物资明细表",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                await ljwzDwgUpdate().getData(publicStore().taskId)
                ljwzDwgUpdate().dwgUpdateFun(th)
                resolve()
            })
        }
    },
    {
        tm: "电缆通道土建表",
        fun(th) {
            return new Promise(async (resolve, reject) => {
                const res = await dltdtjDwgUpdate().getCableChannel(publicStore().taskId)
                publicStore().postMessageMsgCadReport({
                    type: "MsgCad",
                    content: "DLTDTJ",
                    th: th
                }, res.data, []);
                resolve()
            })
        }
    },
    {
        tm: "插入图框图纸更新",
        fun2(th, item) {
            alert(th)
            return new Promise(async (resolve, reject) => {
                const formData = JSON.parse(item.equipmentInfo)
                console.log('JSON.parse(item.equipmentInfo)', formData);
                const res = await crtkDwgUpdate().mxPreview(formData)
                console.log('openUrlTkres', res);
                publicStore().oniframeMessage({
                    type: "cadPreview",
                    content: "Mx_InsertCr",
                    formData: {
                        openUrlTk: res.openUrlTk,
                        openUrlTq: res.openUrlTq,
                        sjdw: formData.sjdw, // 单位
                        gcmc: formData.gcmc, // 项目名称
                        sjjd: formData.sjjd, // 阶段
                        time: formData.time, // 时间
                        th: th, // 图号
                        tzmc: formData.tzmc, // 图纸名称
                        tzbl: formData.tzbl, // 比例
                        tzlx: formData.tzlxName
                    }
                })
                resolve()
            })
        }
    },
    // {
    //     tm: "自动匹配图纸更新",
    //     fun3(th, item) {
    //         return new Promise(async (resolve, reject) => {

    //             console.log('JSON.parse(item.equipmentInfo)', formData);

    //             resolve()
    //         })
    //     }
    // }
]
// export const crtzUpdate = async (list) => {
//     list.forEach(element => {
//         element.equipmentInfo = JSON.parse(element.equipmentInfo)
//     });
//     console.log('crtzUpdateList', list);

//     await crtkDwgUpdate().mxPreview(list)
//     publicStore().oniframeMessage({
//         type: "cadPreview",
//         content: "Mx_InsertCr",
//         formData: {
//             openUrlTk: publicStore().openUrlTk,
//             openUrlTq: publicStore().openUrlTq,
//             sjdw: formData.sjdw, // 单位
//             gcmc: formData.gcmc, // 项目名称
//             sjjd: formData.sjjd, // 阶段
//             time: formData.time, // 时间
//             th: th, // 图号
//             tzmc: formData.tzmc, // 图纸名称
//             tzbl: formData.tzbl, // 比例
//             tzlx: formData.tzlxName
//         }
//     })
// }
export const zdppUpdate = async (list) => {
    list.forEach(element => {
        element.equipmentInfo = JSON.parse(element.equipmentInfo)
    });
    await zdppDwgUpdate().InsertDrawing(list)
    console.log('zdppDwgUpdateList', list);
}